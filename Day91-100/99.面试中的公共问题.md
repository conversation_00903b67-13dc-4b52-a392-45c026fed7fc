## 面试中的公共问题

### 计算机基础

1. TCP/IP模型相关问题。

   > 建议阅读阮一峰的[《互联网协议入门（一）》](http://www.ruanyifeng.com/blog/2012/05/internet_protocol_suite_part_i.html)和[《互联网协议入门（二）》](http://www.ruanyifeng.com/blog/2012/06/internet_protocol_suite_part_ii.html)。

2. HTTP和HTTPS相关问题。

   > 建议阅读阮一峰的[《HTTP 协议入门》](http://www.ruanyifeng.com/blog/2016/08/http.html)和[《SSL/TLS协议运行机制的概述》](http://www.ruanyifeng.com/blog/2014/02/ssl_tls.html)。

3. Linux常用命令和服务。

4. 进程和线程之间的关系。什么时候用多线程？什么时候用多进程？。

5. 关系型数据库相关问题（ACID、事务隔离级别、锁、SQL优化）。

6. 非关系型数据库相关问题（CAP/BASE、应用场景）。

### Python基础

1. 开发中用过哪些标准库和三方库。

   > 标准库：sys / os / re / math / random / logging / json / pickle / shelve / socket / datetime / hashlib / configparser / urllib / itertools / collections / functools / threading / multiprocess / timeit / atexit / abc / asyncio / base64 / concurrent.futures / copy / csv / operator / enum / heapq / http / profile / pstats / ssl / unittest / uuid
   >
   > 三方库：openpyxl / xlrd / xlwt / PyPDF2 / ReportLab / PyYAML / jieba / pillow / requests / urllib3 / responses / aiohttp / BeautifulSoup4 / lxml / pyquery / PyMySQL / psycopg2 / redis / PyMongo / Peewee / SQLAlchemy / alipay / PyJWT / itsdangerous / celery / flower / elasticsearch-dsl-py / PyCrypto / Paramiko / logbook / nose / pytest / coverage / Selenium / lineprofiler / memoryprofiler / matplotlib / pygal / OpenCV

2. 装饰器的作用、原理和实现。

3. 使用过哪些魔法方法。

   > 建议阅读[《Python魔术方法指南》](https://pycoders-weekly-chinese.readthedocs.io/en/latest/issue6/a-guide-to-pythons-magic-methods.html)。

4. 生成式、生成器、迭代器的编写。

5. 列表、集合、字典的底层实现。

6. 垃圾回收相关问题。

7. 并发编程的相关问题。

8. 协程和异步I/O相关知识。

### Django和Flask

1. MVC架构（MTV）解决了什么问题。

2. 中间件的执行流程以及如何自定义中间件。

3. REST数据接口如何设计（URL、域名、版本、过滤、状态码、安全性）。

   > 建议阅读阮一峰的[《RESTful API设计指南》](http://www.ruanyifeng.com/blog/2014/05/restful_api.html)。

4. 使用ORM框架实现CRUD操作的相关问题。

   - 如何实现多条件组合查询 / 如何执行原生的SQL / 如何避免N+1查询问题

5. 如何执行异步任务和定时任务。

6. 如何实现页面缓存和查询缓存？缓存如何预热？

### 爬虫相关

1. Scrapy框架的组件和数据处理流程。
2. 爬取的目的（项目中哪些地方需要用到爬虫的数据）。
3. 使用的工具（抓包、下载、清理、存储、分析、可视化）。
4. 数据的来源（能够轻松的列举出10个网站）。
5. 数据的构成（抓取的某个字段在项目中有什么用）。
6. 反反爬措施（限速、请求头、Cookie池、代理池、Selenium WebDriver、RoboBrowser、TOR、OCR）。
7. 数据的体量（最后抓取了多少数据，多少W条数据或多少个G的数据）。
8. 后期数据处理（持久化、数据补全、归一化、格式化、转存、分类）。

### 数据分析

1. 科学运算函数库（SciPy和NumPy常用运算）。
2. 数据分析库（Pandas中封装的常用算法）。
3. 常用的模型及对应的场景（分类、回归、聚类）。
4. 提取了哪些具体的指标。
5. 如何评价模型的优劣。
6. 每种模型实际操作的步骤，对结果如何评价。

### 项目相关

1. 项目团队构成以及自己在团队中扮演的角色（在项目中的职责）。
2. 项目的业务架构（哪些模块及子模块）和技术架构（移动端、PC端、后端技术栈）。
3. 软件控制管理相关工具（版本控制、问题管理、持续集成）。
4. 核心业务实体及其属性，实体与实体之间的关系。
5. 用到哪些依赖库，依赖库主要解决哪方面的问题。
6. 项目如何部署上线以及项目的物理架构（Nginx、Gunicorn/uWSGI、Redis、MongoDB、MySQL、Supervisor等）。
7. 如何对项目进行测试，有没有做过性能调优。
8. 项目中遇到的困难有哪些，如何解决的。