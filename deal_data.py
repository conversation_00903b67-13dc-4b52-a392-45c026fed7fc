import sensorsanalytics


# 解析 csv
import csv
filePath = "/Users/<USER>/Desktop/test.csv"
with open(filePath, mode='r', encoding='UTF-8') as f:
    reader = csv.DictReader(f)
    for line in reader:
        print("==1==")
        print(line)
        copy = line.copy()
        for k in copy.keys():
            if copy[k] == '' or copy[k] is None:
                del copy[k]
            print(copy[k][0:2])
        print(copy)
        print("==2==")

# # 时间处理
# import time
# import datetime
# from datetime import timedelta
# # 把字符串解析为时间对象
# print(datetime.datetime.strptime("2022-09-09 09:08:28", "%Y-%m-%d %H:%M:%S") + timedelta(days=1,hours=8))
# # 把时间对象转换为字符串
# print(datetime.datetime.strftime(datetime.datetime.today(), "%Y-%m-%d %H:%M:%S"))
#
# # 解析 excel
# import xlrd
# filePath = "/Users/<USER>/Desktop/test.xlsx"
# wb = xlrd.open_workbook(filePath)
# # 获取第一张工作表
# sheet = wb.sheet_by_index(0)
# # 获取第一行作为标题
# titles = sheet.row_values(0)
# # 存放每一行转换后的数据
# data_list = []
# # 从第二行开始遍历
# for r in range(1, sheet.nrows):
#     properties = {}
#     values = sheet.row_values(r)
#     for col in range(sheet.ncols):
#         title = titles[col]
#         value = values[col]
#         if value is not None or value != '':
#             properties[title] = value
#         data_list.append(properties)
# print(data_list)

# 埋点上报
SA_SERVER_URL='http://10.129.25.96:8106/sa?project=default'
# 发送数据的超时时间，单位秒
SA_REQUEST_TIMEOUT = 10
# 当缓存的数据量达到参数值时，批量发送数据
SA_BULK_SIZE = 100
# consumer = sensorsanalytics.ConcurrentLoggingConsumer("/Users/<USER>/DeskTop/s.txt")
consumer = sensorsanalytics.BatchConsumer(SA_SERVER_URL, SA_BULK_SIZE, SA_REQUEST_TIMEOUT)
sa = sensorsanalytics.SensorsAnalytics(consumer)
# distinct_id='15008400985'
distinct_id='9771C579-71F0-4650-8EE8-8999FA717762'
sa.track(distinct_id, "itemShow", {'Total': 1, 'First': False, 'City': 'bj'}, is_login_id=False)
properties = {'city':'bj'}
# sa.profile_set(distinct_id, properties, is_login_id=False)
# sa.track_signup(distinct_id, '9771C579-71F0-4650-8EE8-8999FA717762')
sa.flush()
sa.close()