# CrewAI 基本概念

本文简要梳理 CrewAI 的关键概念、常见协作模式与实践建议，便于快速上手与团队设计。

## 1. 核心组成

- Agent（代理）
  - 具备明确角色（Role）、目标（Goal）、技能（Tools）的自治体。
  - 可拥有记忆（Memory）与知识（Knowledge），在对话/任务中保持上下文。
  - 常见属性：角色、目标、约束、可用工具、提示模板、模型配置（温度、上下文窗口等）。

- Task（任务）
  - 可交付的工作单元，描述要做什么、由谁来做、输出期望与验收标准。
  - 常含字段：描述、输入/上下文、输出格式、验收标准、优先级、依赖（depends_on）、执行代理。

- Crew（团队）
  - 由多个 Agent 与 Task 构成的编排容器，负责整体流程的运行、调度与状态管理。
  - 提供流程策略（Process / Orchestration）与通信机制（消息路由、共享上下文）。

- Tools（工具）
  - 代理可调用的外部能力或函数，例如：Web 搜索、浏览器、数据库、RAG 检索、代码执行、文件读写等。
  - 建议采用最小权限与沙箱，限制副作用与成本（速率限制、超时、白名单）。

- Process / Orchestrator（流程/编排）
  - 定义任务与代理的协作方式，如顺序（Sequential）、并行（Parallel）、层级（Hierarchical/Manager-Worker）等。
  - 负责依赖解析、重试策略、异常处理、路由转发与上下文共享。

- Memory（记忆）
  - 短期记忆：本次运行的对话与中间结果。
  - 长期记忆：可持久化到向量库/数据库，支持跨会话复用与检索增强。

- Knowledge（知识库）
  - 文档、FAQ、规范、代码库索引等静态资料。
  - 通常与 RAG（检索增强生成）结合，提高可用性与准确性。

- Evaluation / Feedback（评估/反馈）
  - 自动或人工评审任务输出，支持反思（Reflection）、自我修正（Self-Refine）、多轮改进。

- Models / Prompts（模型与提示）
  - 模型选择、参数（温度、Top‑p）、系统/角色提示词与模板。
  - 控制风格、一致性与成本。

## 2. 常见协作模式

- 顺序流水线（Sequential）
  - 任务按依赖拓扑顺序执行；稳定、易审计，适合流程清晰的场景。
- 并行协作（Parallel）
  - 可独立的子任务并行加速，需关注资源配额与一致性合并。
- 层级（Manager-Worker）
  - 经理代理负责拆解/分配/验收，执行代理完成具体任务；适合开放性问题与长流程。
- 评审回路（Reviewer/QA）
  - 专门的评审代理对中间/最终输出进行质量把关，必要时触发返工。

## 3. 典型工作流

1. 明确目标与成功标准（Definition of Done）
2. 设计角色与代理能力边界（最小可行能力集）
3. 准备工具与权限（白名单、速率、超时）
4. 任务拆解与依赖建模（输入/输出/验收标准）
5. 选择流程策略（顺序/并行/层级，是否需要评审回路）
6. 运行与可观测性（日志、事件、链路ID、成本统计）
7. 评估与复盘（失败分析、记忆沉淀、提示与工具迭代）

## 4. 常见用例

- 内容生产：调研 → 提纲 → 初稿 → 评审 → 定稿
- 代码助理：需求澄清 → 设计 → 实现 → 测试 → 代码评审
- 数据分析：问题分解 → 数据获取/清洗 → 探索分析 → 可视化/报告
- RAG 助手：检索 → 综合 → 草稿 → 校对 → 定稿
- 增长/运营：线索挖掘 → 外联撰写 → A/B 迭代 → 报告

## 5. 最佳实践

- 明确验收标准：结构化输出格式（JSON/模板）、质量门槛、边界条件。
- 工具最小权限：按需开放、加速观测异常（超时/重试/熔断）。
- 小步快跑：先用少量代理与任务跑通，再扩展复杂度。
- 可观测性与可重现：固定模型/seed、版本化提示与配置、记录成本。
- 失败优先：为超时、调用失败、空结果设计替代路径与降级方案。
- 成本控制：并发上限、调用预算、缓存与复用中间结果。
- 数据与安全：避免泄露敏感数据，控制长上下文中可见范围。

## 6. 极简示意（伪代码）

> 说明：以下为概念性示意，实际 API/类名以所用版本文档为准。

```python
from crewai import Agent, Task, Crew, Process

researcher = Agent(
    role="研究员",
    goal="收集并结构化主题A的要点",
    tools=[/* web_search, browser 等 */],
)

writer = Agent(
    role="撰稿人",
    goal="基于调研要点撰写1000字中文文章",
)

task_research = Task(
    description="调研主题A并输出要点列表（含参考链接）",
    agent=researcher,
    expected_output="Markdown 列表，含3-5个可靠来源",
)

task_write = Task(
    description="根据调研要点撰写文章，附摘要与结论",
    agent=writer,
    depends_on=[task_research],
    expected_output="结构化 Markdown 文章（标题/摘要/正文/结论）",
)

crew = Crew(
    agents=[researcher, writer],
    tasks=[task_research, task_write],
    process=Process.sequential,  # 或 hierarchical / parallel
)

result = crew.kickoff()
print(result)
```

## 7. 术语速查

- Agent：自治体（角色+目标+工具）
- Task：工作单元（描述+输出+验收+依赖）
- Crew：团队编排容器
- Tool：可调用函数/服务
- Process：运行/调度策略
- Memory/Knowledge：记忆与知识库
- Evaluation：评审/反馈/改进
- Prompt/Model：提示模板与模型配置

## 8. 参考与延伸

- 官方文档与示例仓库（根据使用版本查阅）
- RAG、Reflexion、自我一致性等相关论文与实践
- 生产化工程：可观测性、灰度、回滚、合规与隐私