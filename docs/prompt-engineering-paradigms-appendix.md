# Prompt Engineering 范式附录（扩展版）

最后更新：2025-08-25

说明
- 本附录扩展“其余范式速查清单”，提供通俗解释与可复制模板。
- 结构：概念/适用/真实任务/输入/提示词/示例输出/要点&常见坑。
- 与主文档的关系：主文档提供概览与高频范式详例；本附录针对其余范式补足细节。

目录
1. Context + Instruction + Input + Output（CIIO 结构化）
2. Zero-shot
3. One-shot
4. Role/Persona
5. Scratchpad
6. CoT + Tool Use
7. Self-Ask
8. Graph-of-Thought（GoT）
9. Reflexion
10. Active Prompting
11. Socratic Prompting
12. Debate / Self-Debate
13. Constitutional Prompting
14. Guardrail/Safety
15. Constraint-Driven
16. Directional Stimulus
17. Prompt Chaining
18. Meta-Prompting
19. Example-Selection / In-Context Retrieval
20. Auto-CoT
21. Verification / Self-Critique
22. Double Check / Cross-Check
23. Confidence Calibration
24. Style Transfer / Tone Control
25. Multi-Modal Grounding
26. EBNF/Regex Constrained Output
27. Safety Red-Teaming
28. Planner-Executor
29. Router/Selector
30. Few-shot CoT
31. Programmatic Templates
32. Few-shot with Counterexamples
33. Ask-for-More
34. Stepwise Verification with Tests
35. Explanation then Decision
36. Role Triad: Creator–Critic–Resolver
37. Knowledge-Grounded with Citations
38. Memory-Augmented
39. Curriculum/Scaffolded
40. Calibrated Refusal + Alternatives
41. Pseudo-code First
42. Unit-of-Work
43. Role + Tools + Safety Bundle

---

## 1) Context + Instruction + Input + Output（CIIO 结构化）

概念（通俗）
- 把提示拆成4块：上下文（Context）、要做什么（Instruction）、具体输入（Input）、要求的输出格式（Output）。
- 好处：像“表单”一样明确，模型更容易稳定产出。

适用
- 生产场景、需要稳定结构化输出、对格式要求高的任务。

真实任务
- 基于知识片段回答问题，并输出固定 JSON。

输入
- 知识片段（含 doc_id）与用户问题。

提示词（模板，可直接套用）
```text
Context（上下文，权威来源，仅此可用）：
{{DOCS}}

Instruction（要做什么）：
- 回答用户问题
- 只能使用 Context 中的事实
- 如无法从 Context 得到答案，回答“信息不足”

Input（用户输入）：
{{QUESTION}}

Output（输出格式，严格遵循）：
{
  "answer": "string（若信息不足则为'信息不足'）",
  "citations": ["doc_id#:page#（出现顺序即可）"]
}
仅输出 JSON，不要任何解释。
```

示例输出
```json
{
  "answer": "餐补上限为 150 元/天；机票需经济舱，超标需审批。",
  "citations": ["doc_12:3", "doc_18:2"]
}
```

要点 & 常见坑
- 要点：4段清晰，尤其 Output 给出严格 Schema；禁止额外文字。
- 坑：未声明“只能用 Context”；Schema 不够具体导致结构漂移；输出多余解释导致解析失败。

---

## 2) Zero-shot（零样本）

概念
- 无示例，直接给任务与约束。

适用
- 简单分类/摘要/格式化。

真实任务
- 30字内摘要。

输入
- 一段新闻。

提示词
```text
将下段文本压缩为≤30字摘要；不得虚构；仅输出摘要：
{{TEXT}}
```

示例输出
- “Q2出货同比增18%，ASP降5%。”

要点/坑
- 明确长度/风格约束；避免让模型自由发挥。

---

## 3) One-shot（单样本）

概念
- 1个高质量示例锁定格式/风格。

适用
- 需要稳定格式，但样本不多。

真实任务
- 将“标题, 日期”转 JSON。

输入
- “Report A, 2023-01-02”

提示词
```text
示例
输入："Report A, 2024-05-01"
输出：{"title":"Report A","date":"2024-05-01"}

现在处理：{{INPUT}}
仅输出 JSON。
```

示例输出
```json
{"title":"Report A","date":"2023-01-02"}
```

要点/坑
- 示例要“像素级”匹配目标；避免多示例相互矛盾。

---

## 4) Role/Persona（角色/人设）

概念
- 设定角色影响语气与流程。

适用
- 法务/审计/教练/专家场景。

真实任务
- 法务条款审阅。

输入
- 合同片段。

提示词
```text
你是资深法务。先列出关键风险（条款+原因），再给逐条修订建议（替换文本）。
文本：{{CONTRACT}}
仅输出：
- 风险清单：[...]
- 修订建议：[...]
```

示例输出（片段）
- 风险：保密期限未定义…；建议：补充“自终止起3年”。

要点/坑
- 角色+流程+语气三要素齐全；避免只改语气不改流程。

---

## 5) Scratchpad（草稿推理）

概念
- 允许中间草稿/计算，最后给结论。

适用
- 有中间计算/推导的题。

真实任务
- 分数计算题。

提示词
```text
Scratchpad（中间推导）：
（在此逐步推算）

最终答案（仅一行）：
```

示例输出（片段）
- 最终答案：7/12

要点/坑
- 控制“最终答案”只有一行；草稿不等于最终输出。

---

## 6) CoT + Tool Use（思维链+工具）

概念
- 分步推理，遇计算/事实查询调用工具。

适用
- 需要计算器/检索/API。

真实任务
- 计算预算是否足够（汇率工具）。

提示词
```text
Thought: 需要 USD→CNY 汇率
Action: get_fx_rate[{"base":"USD","quote":"CNY"}]
Observation: {{RATE}}
Thought: 500*{{RATE}} 与 2800 比较
Final Answer: …
```

示例输出（片段）
- Final Answer: 足够（按7.25约3625）

要点/坑
- 记录 Observation；避免“凭空数据”。

---

## 7) Self-Ask（自提问分解）

概念
- 模型先提子问题再解答。

适用
- 开放问题需要拆解。

真实任务
- 推动项目延期的主要因素？

提示词
```text
请先列≤5个子问题并逐一回答；必要时调用工具；最后整合结论与建议。主题：{{TOPIC}}
```

示例输出（片段）
- 子问/子答…；结论：资源瓶颈与需求变更…

要点/坑
- 控制子问题数量；覆盖不确定性。

---

## 8) Graph-of-Thought（GoT 思维图）

概念
- 多路径并行、合并/纠错形成最终方案。

适用
- 需要合并多思路/约束。

真实任务
- 方案合成（成本/体验/风险）。

提示词
```text
生成3条方案；在“关键节点”相互校正与合并；输出合成方案与合并理由（列节点）。
```

示例输出（片段）
- 合并理由：保留A的低成本与B的体验优势…

要点/坑
- 说明节点/边含义；合并标准清晰。

---

## 9) Reflexion（自反思/记忆）

概念
- 自评初稿→修订→沉淀经验。

适用
- 迭代优化/长期任务。

真实任务
- 文案改进并保存经验。

提示词
```text
初稿：{{DRAFT}}
自评：列错误与改进点
修订稿：…
经验备忘：列3条可复用原则
```

示例输出（片段）
- 经验：避免夸大；给量化指标；引用来源…

要点/坑
- 输出“问题→修订→经验”的闭环。

---

## 10) Active Prompting（主动求证）

概念
- 不足信息先澄清问题。

适用
- 需求不全/歧义。

真实任务
- 生成技术方案前澄清。

提示词
```text
若关键信息缺失，请先提出≤3个澄清问题并等待用户回答。任务：{{TASK}}
```

示例输出（片段）
- 问题1：目标用户规模？问题2：SLA 要求？

要点/坑
- 问少而准；聚焦阻塞项。

---

## 11) Socratic Prompting（苏格拉底）

概念
- 用问题引导学习/推理。

适用
- 教学/辅导。

真实任务
- 讲解二分查找。

提示词
```text
请设计5个由浅入深的问题，并给每题的要点答案。主题：二分查找
```

示例输出（片段）
- Q1… A要点：单调性与有序…

要点/坑
- 每问推进一小步。

---

## 12) Debate / Self-Debate（辩论/自辩）

概念
- 两方辩论，Judge 裁决。

适用
- 多立场评估。

真实任务
- 是否采用微服务。

提示词
```text
DebaterA（支持）/B（反对）分别给论据；Judge 按“准确/有据/适配性”打分与裁决。
```

示例输出（片段）
- 裁决：分阶段引入…

要点/坑
- 定义评价维度与权重。

---

## 13) Constitutional Prompting（宪法式）

概念
- 用“原则集”指导自评修订。

适用
- 安全/语气/公正。

真实任务
- 对照原则纠偏。

提示词
```text
原则：中立、公正、可验证
请对照原则自评并修订，列出修改点
草稿：{{DRAFT}}
```

示例输出（片段）
- 修改点：删除带偏见的形容词…

要点/坑
- 原则可操作且可检查。

---

## 14) Guardrail/Safety（安全护栏）

概念
- 明确允许/禁止/处置策略。

适用
- 合规生产环境。

真实任务
- 处理敏感请求。

提示词
```text
若请求涉及个人敏感数据或违法，请拒绝并提供3条安全替代。请求：{{REQ}}
```

示例输出（片段）
- 无法协助… 替代：使用匿名数据…

要点/坑
- 拒绝语模板化+可执行替代。

---

## 15) Constraint-Driven（约束驱动）

概念
- 明确长度/语气/时间/资源等硬约束。

适用
- 生产质量输出。

真实任务
- ≤120字摘要，含1个数据点。

提示词
```text
≤120字，中性语气，必须包含1个数据点，不引用外部来源。文本：{{TEXT}}
```

示例输出（片段）
- …同比+18%…

要点/坑
- 约束清晰且可检查。

---

## 16) Directional Stimulus（方向性提示）

概念
- 给“检查方向”，不直接给步骤/答案。

适用
- 需要启发/探索。

真实任务
- 算法正确性评审。

提示词
```text
请重点检查边界值、反例与不变量，再给结论。题：{{PROBLEM}}
```

示例输出（片段）
- 边界：n=0；反例：…

要点/坑
- 提方向不剧透答案。

---

## 17) Prompt Chaining（提示链）

概念
- 多阶段串行，每步产出供下一步。

适用
- 写作/抽取/评审流水线。

真实任务
- 需求→草稿→审稿→定稿。

提示词
```text
阶段1：提炼要点
阶段2：据要点写草稿
阶段3：批注问题与修订
阶段4：输出定稿
输入：{{TEXT}}
```

示例输出（片段）
- 阶段1要点… 最终稿…

要点/坑
- 显式传递阶段字段。

---

## 18) Meta-Prompting（元提示）

概念
- 让模型优化你的提示。

适用
- 提示设计阶段。

真实任务
- 优化现有提示。

提示词
```text
改写该提示，补齐目标/约束/输出格式，并列出改进点清单：{{PROMPT}}
```

示例输出（片段）
- 改写版… 改进点…

要点/坑
- 产出“新提示+变化说明”。

---

## 19) Example-Selection / In-Context Retrieval（示例选择）

概念
- 从样本库按相似度选少样本。

适用
- 多领域/异构输入。

真实任务
- 为分类任务选3个最相似示例。

提示词（控制层伪代码）
```text
examples = ANN.search(embedding(input), k=3, diversify=True)
prompt = build_fewshot_prompt(examples, input)
```

示例输出（片段）
- 选中示例ID：[e12,e37,e44]

要点/坑
- 相似度与多样性权衡。

---

## 20) Auto-CoT（自动思维链示例）

概念
- 自动为各类题型生成 CoT 示例库。

适用
- 规模化引入 CoT。

真实任务
- 构建题型→示例库。

提示词（控制层）
```text
cluster = cluster_by_task_type(dataset)
cot_example = generate_best_cot_per_cluster(cluster)
save(cot_example)
```

示例输出（片段）
- ClusterA→CoT示例…

要点/坑
- 质量优先于数量。

---

## 21) Verification / Self-Critique（自校验）

概念
- 生成→按规则/测试校验→修订重试。

适用
- 代码/合规/数学。

真实任务
- 校验摘要是否覆盖关键点。

提示词
```text
草稿：{{DRAFT}}
规则：必须包含A/B/C
请列未覆盖项并修订；输出修订稿与通过/失败摘要。
```

示例输出（片段）
- 未覆盖：B；修订后通过。

要点/坑
- 引入可执行检查点。

---

## 22) Double Check / Cross-Check（双重复核）

概念
- 两解独立求解与对照取优。

适用
- 高风险决策。

真实任务
- 方案A/B 对比并合并。

提示词
```text
独立给出方案A/B；列差异与取舍理由；输出最终合并方案。
```

示例输出（片段）
- 最终：采A的稳定性+B的易用性。

要点/坑
- 保证A/B独立性。

---

## 23) Confidence Calibration（置信度校准）

概念
- 报告答案置信度、假设与未知点。

适用
- 决策支持。

真实任务
- 给结论+置信度。

提示词
```text
输出：答案；置信度(0-100%)；关键假设；未知/风险。
```

示例输出（片段）
- 置信度：70%；假设：样本具代表性…

要点/坑
- 置信度必须配合假设说明。

---

## 24) Style Transfer / Tone Control（风格/语气）

概念
- 面向特定受众重写，保持事实。

适用
- 营销/本地化。

真实任务
- 面向财务受众改写。

提示词
```text
为“财务受众”，用“正式语气”重写。保持事实与数据不变。
```

示例输出（片段）
- 更正式的改写文本…

要点/坑
- 禁止引入新事实。

---

## 25) Multi-Modal Grounding（多模态）

概念
- 图像/音频/视频+文字联合推理。

适用
- 图表/界面/视觉问答。

真实任务
- 描述图表并给2个数字。

提示词
```text
请描述趋势并给出两项数字（标注单位）：峰值、均值。图像：{{IMG}}
```

示例输出（片段）
- 峰值=120（件/小时），均值=85（件/小时）

要点/坑
- 标注量纲与单位。

---

## 26) EBNF/Regex Constrained Output（语法约束）

概念
- 用语法或正则约束输出形状。

适用
- DSL/配置/ID生成。

真实任务
- 生成形如 ABC-123456 的ID。

提示词
```text
仅输出匹配 ^[A-Z]{3}-[0-9]{6}$ 的ID；否则输出 "INVALID"。
```

示例输出
- ABC-123456

要点/坑
- 明确兜底策略。

---

## 27) Safety Red-Teaming（安全对抗测试）

概念
- 受控环境中探测安全边界。

适用
- 安全评估。

真实任务
- 设计三种攻击变体。

提示词
```text
在以下范围内设计3个攻击变体，记录模型响应与拒绝理由（仅测试用途）。
范围：{{SCOPE}}
```

示例输出（片段）
- 变体1… 拒绝理由…

要点/坑
- 仅测试环境，遵守合规。

---

## 28) Planner-Executor（规划-执行）

概念
- 规划器产计划，执行器按计划执行。

适用
- 多步骤自动化/多工具。

真实任务
- 构建→评估→发布流水线。

提示词
```text
Planner：列步骤（输入/输出/工具）
Executor：逐步执行并记录结果与偏差
任务：{{TASK}}
```

示例输出（片段）
- 步骤1→结果…

要点/坑
- 分工明确，可观测。

---

## 29) Router/Selector（路由/选择）

概念
- 按任务类型路由到专用路径。

适用
- 多任务平台。

真实任务
- 请求分类路由。

提示词（控制层）
```text
if numeric → CoT+calculator
elif factual → RAG
else → Few-shot
```

示例输出（片段）
- 路由：RAG → 答案：…

要点/坑
- 判别规则可测试。

---

## 30) Few-shot CoT（少样本思维链）

概念
- 示例中包含推理过程，固化风格。

适用
- 数学/逻辑模式题。

真实任务
- 解题并输出 Reasoning + Answer。

提示词
```text
示例Q/A含Reasoning；现在解：先Reasoning再Answer，保持同风格。题：{{Q}}
```

示例输出（片段）
- Reasoning: …；Answer: …

要点/坑
- 控制推理篇幅与深度。

---

## 31) Programmatic Templates（参数化模板）

概念
- 用模板变量统一大量提示。

适用
- 生产/AB测试。

真实任务
- 批量生成一致输出。

提示词（模板）
```text
{{ROLE}}

任务：{{TASK}}
约束：{{CONSTRAINTS}}
输出格式：{{FORMAT}}
示例：{{EXAMPLES}}
```

示例输出（片段）
- 符合模板的结构化输出。

要点/坑
- 版本化与监控。

---

## 32) Few-shot with Counterexamples（反例启发）

概念
- 用“坏例→正例”成对提示避坑。

适用
- 易错任务。

真实任务
- 时间格式化常见错误。

提示词
```text
坏例：2025/8/1 → 错误：非ISO
好例：2025-08-01
请将输入转为YYYY-MM-DD：{{DATE_TEXT}}
```

示例输出
- 2025-08-01

要点/坑
- 选“最常错”的反例。

---

## 33) Ask-for-More（请求更多信息）

概念
- 列缺失信息再执行。

适用
- 需求采集。

真实任务
- 执行前提问。

提示词
```text
为完成{{TASK}}，请先列出≤5个关键缺失信息并按优先级排序；待确认后继续。
```

示例输出（片段）
- 1) 预算 2) SLA …

要点/坑
- 聚焦阻塞项。

---

## 34) Stepwise Verification with Tests（测试驱动）

概念
- 先写测试再实现。

适用
- 代码/算法。

真实任务
- 函数满足规格。

提示词
```text
请先生成3条单测（含输入/期望输出）；再给实现通过测试；若失败，列失败点与修正。
```

示例输出（片段）
- 测试通过：3/3

要点/坑
- 先测后解，便于回归。

---

## 35) Explanation then Decision（先解释后决策）

概念
- 先给理由与打分，再决策。

适用
- 评审/优先级排序。

真实任务
- 方案评估。

提示词
```text
标准与权重：{{TABLE}}
请逐项打分与理由；汇总权重得分；给出决策。
```

示例输出（片段）
- 方案B 8.6分，入选

要点/坑
- 先理由后结论。

---

## 36) Role Triad: Creator–Critic–Resolver（三角角色）

概念
- 起草→批评→整合定稿。

适用
- 高质量输出。

真实任务
- 报告成稿。

提示词
```text
Creator：输出草稿
Critic：列问题/改进点
Resolver：修订并输出最终稿
```

示例输出（片段）
- 最终稿：…

要点/坑
- 三角色职责分明。

---

## 37) Knowledge-Grounded with Citations（引用）

概念
- 只用来源内容作答并标注引用。

适用
- 事实性任务。

真实任务
- 带引用回答。

提示词
```text
仅基于以下来源回答，并用 [n] 标注；若不足，答“信息不足”。来源：{{DOCS}}；问题：{{Q}}
```

示例输出（片段）
- … [2]

要点/坑
- 严禁无来源扩展。

---

## 38) Memory-Augmented（带记忆）

概念
- 持久化偏好/事实用于个性化。

适用
- 长期会话/个性化。

真实任务
- 遵从用户偏好。

提示词
```text
用户画像：{{PROFILE}}
若相关请遵循；需更新记忆请先确认再使用。
```

示例输出（片段）
- 已按“简洁风格”输出…

要点/坑
- 最小必要记忆；透明确认。

---

## 39) Curriculum/Scaffolded（课程式）

概念
- 由浅入深，结合反馈迭代。

适用
- 教学/技能培养。

真实任务
- 三阶段学习计划。

提示词
```text
Level1 基础→反馈→Level2 进阶→反馈→Level3 综合；每级仅引入少量新概念。主题：{{TOPIC}}
```

示例输出（片段）
- Level1目标/任务/评估…

要点/坑
- 小步快跑。

---

## 40) Calibrated Refusal + Alternatives（合规拒绝）

概念
- 合规拒绝 + 建设性替代方案。

适用
- 生产合规。

真实任务
- 处理违规请求。

提示词
```text
若请求违规，请说明原因并提供3条可行的安全替代方案。
```

示例输出（片段）
- 无法协助… 替代：公开数据集…

要点/坑
- 替代要可落地。

---

## 41) Pseudo-code First（先伪代码）

概念
- 先伪代码/复杂度，再实现。

适用
- 编程任务。

真实任务
- 实现去重并保持顺序。

提示词
```text
先给伪代码（含时间/空间复杂度），再给 Python 实现，最后用3个用例验证。
```

示例输出（片段）
- 用例通过：3/3

要点/坑
- 实现紧随伪代码结构。

---

## 42) Unit-of-Work（工作单元化）

概念
- 每次只做一个原子任务。

适用
- 确定性流水线。

真实任务
- 抽取日期。

提示词
```text
单一目标：输出 YYYY-MM-DD 或 null；仅输出该值。文本：{{TEXT}}
```

示例输出
- 2025-08-25

要点/坑
- 极度聚焦一个输出。

---

## 43) Role + Tools + Safety Bundle（角色+工具+安全）

概念
- 角色设定 + 工具能力 + 安全策略的组合。

适用
- 生产级助理/智能体。

真实任务
- 数据分析问答。

提示词
```text
角色：数据分析师
工具：SQL/检索
安全：不输出个人隐私
任务：{{TASK}}
输出格式：{"result":string,"used_tools":string[]}
```

示例输出（片段）
```json
{"result":"……","used_tools":["sql","retrieval"]}
```

要点/坑
- 三者缺一不可；格式可解析。

---