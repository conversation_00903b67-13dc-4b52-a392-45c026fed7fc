# 三份分析文档的细节对齐整合表（AutoGen / LangGraph / CrewAI 对同三问的处理全量细节）

| 问题 | AutoGen | LangGraph | CrewAI |
|---|---|---|---|
| 对话一：你好 | 1) 定义 Agents：user_proxy（代表用户、可执行代码）；assistant（LLM 助手）<br>2) 初始化对话：user_proxy 将消息转给 assistant<br>3) LLM 调用：无需工具/代码<br>- Prompt: System: You are a helpful assistant.；User: 你好<br>- LLM 返回(文本): Assistant: 你好！有什么可以帮你的吗？<br>4) 响应用户：assistant → user_proxy；双方各自记录对话历史 | 1) 定义 Agent 和 Tools：对话 Agent + 工具集（此例不需特定工具）<br>2) 初始化 Graph：StatefulGraph，state 含对话历史<br>- 初始 state: {"messages": ["Human: 你好"]}<br>3) 定义节点：call_model 调用 LLM 生成回应<br>4) 构建流程：entry_point=call_model，形成可循环对话<br>5) LLM 调用<br>- Prompt: System: You are a helpful assistant.；User: 你好<br>- 返回: Assistant: 你好！有什么可以帮你的吗？<br>6) 更新 state：{"messages": ["Human: 你好","AI: 你好！有什么可以帮你的吗？"]} | 1) 定义 Agent 和 Task<br>- Agent: ConversationalAgent；role=友好对话助手；goal=与用户友好交谈<br>- Task: 描述“回应用户的问候”<br>2) 组建并启动 Crew：将 Agent+Task 组成 Crew；Kickoff 后 Agent 接收任务<br>3) LLM 调用（按角色/任务生成回复）<br>- Prompt: Role/Goal/Task 描述 + User Input: 你好<br>- 返回(文本): 你好！有什么可以帮你的吗？<br>4) 任务完成：返回 Agent 文本；CrewAI 保留执行上下文 |
| 对话二：请帮我查询一下数据库中有多少用户 | 1) 注册函数(工具)：定义 Python 函数 query_database 并注册到 user_proxy（assistant 知道工具可用，且由 user_proxy 执行）<br>2) 对话继续：user_proxy 携带历史把新问题发给 assistant<br>3) LLM 决定使用工具：从函数签名识别合适工具<br>- Prompt(含函数定义与历史)：System: You are a helpful assistant. You can use the following function by generating Python code.；def query_database(query: str) -> str: """Executes a SQL query and returns the result."""；Conversation History: User: 你好 / AI: 你好！有什么可以帮你的吗？；User: 请帮我查询一下数据库中有多少用户<br>- LLM 返回(代码)：生成 Python 代码调用工具：query_database(query="SELECT COUNT(*) FROM users;")<br>4) 代码执行：assistant 将代码发给 user_proxy 执行<br>- 函数返回(JSON)：{"result": [{"count": 12345}]}<br>5) 结果反馈与最终回答：user_proxy 将执行结果回传，assistant 再次调用 LLM 组织答案<br>- Prompt(简化)：… + [Code Execution Result]: {"result": [{"count": 12345}]} + “Now, provide a final answer…”<br>- LLM 返回(文本)：Assistant: 数据库中共有 12345 位用户。<br>6) 响应用户：assistant → user_proxy 呈现 | 1) State 传入：含“你好”历史 + 新问题<br>- state: {"messages": ["Human: 你好","AI: 你好！有什么可以帮你的吗？","Human: 请帮我查询一下数据库中有多少用户"]}<br>2) Router(路由判断)：entry_point=router，使用具函数调用能力的 LLM 判断是否用工具<br>- Router Prompt：说明可用工具 query_database、是否需要使用及参数；User: 请帮我查询一下数据库中有多少用户<br>- LLM 返回(Tool Call)：{"tool_calls":[{"name":"query_database","arguments":{"query":"SELECT COUNT(*) FROM users;"}}]}<br>3) 条件分支：含 tool_calls → 走 conditional edge 至 tool_node<br>4) Tool Node：执行 query_database，参数 {"query":"SELECT COUNT(*) FROM users;"}<br>- Tool 返回(JSON)：{"result":[{"count":12345}]}<br>5) LLM 调用(生成最终答案)：把工具输出+原问题传给 call_model<br>- Prompt：System: You are a helpful assistant.；User: 请帮我查询一下数据库中有多少用户；Tool Output: {"result":[{"count":12345}]}；要求生成自然语言回答<br>- 返回：Assistant: 数据库中共有 12345 位用户。<br>6) 更新 state：记录工具调用、结果和最终回复 | 1) 定义 Tool：用 @tool 定义 query_database<br>2) 定义 Agent：DatabaseAnalystAgent；Role=数据库分析专家；Goal=使用工具查询数据库获取答案；Tools=包含 query_database<br>3) 定义 Task：Description="查询数据库中的用户总数。"；Expected Output="一个包含用户总数的句子。"；Agent=DatabaseAnalystAgent<br>4) 组建并启动 Crew：Agent+Task → Crew → 启动<br>5) Agent 执行 Task：LLM 识别用工具并生成合适 SQL 参数<br>- Tool Call：query_database(query="SELECT COUNT(*) FROM users;")<br>- Tool 返回(JSON)：{"result":[{"count":12345}]}<br>6) 综合并返回结果：LLM 将结构化数据转成自然语言以满足 expected_output<br>- 最终答案：数据库中共有 12345 位用户。 |
| 对话三：这些用户的性别比例是多少？ | 1) 上下文感知：user_proxy 携带完整历史发给 assistant；assistant 基于上下文理解“这些用户”为数据库所有用户<br>2) LLM 再次生成代码：继续使用 query_database<br>- Prompt(简化)：…(包含上一问及其结果)；User: 这些用户的性别比例是多少？<br>- LLM 返回(代码)：query_database(query="SELECT gender, COUNT(*) FROM users GROUP BY gender;")<br>3) 代码执行：assistant → user_proxy 执行<br>- 函数返回(JSON)：{"result":[{"gender":"male","count":6200},{"gender":"female","count":6145}]}<br>4) 生成最终回答：user_proxy 回传结果；assistant 的 LLM 总结生成文字<br>- Prompt(简化)：…；[Code Execution Result]: {"result":[{"gender":"male","count":6200},{"gender":"female","count":6145}]}；“Now, provide a final answer…”<br>- LLM 返回(文本)：Assistant: 好的，用户性别比例如下：男性用户有 6200 人，女性用户有 6145 人，比例接近 1:1。<br>5) 结束对话：双方记录完整历史 | 1) State 传入：包含之前所有历史 + 新问题“这些用户的性别比例是多少？”<br>2) Router：基于历史理解指代，判断需用工具<br>- Router Prompt：含工具说明 + Conversation History（上一问与回答）+ 新的 User 问题<br>- LLM 返回(Tool Call)：{"tool_calls":[{"name":"query_database","arguments":{"query":"SELECT gender, COUNT(*) FROM users GROUP BY gender;"}}]}<br>3) 条件分支与工具调用：走 tool_node 执行查询<br>- Tool 返回(JSON)：{"result":[{"gender":"male","count":6200},{"gender":"female","count":6145}]}<br>4) LLM 调用(生成最终答案)：结果送回 call_model<br>- Prompt：System: You are a helpful assistant.；User: 这些用户的性别比例是多少？；Tool Output: {"result":[{"gender":"male","count":6200},{"gender":"female","count":6145}]}；生成自然语言回答<br>- 返回：Assistant: 好的，用户性别比例如下：男性用户有 6200 人，女性用户有 6145 人，比例接近 1:1。<br>5) 更新 state：完整交互记录入 state | 1) 新 Task 并传入上下文：Description="查询数据库中用户的性别分布情况。"；Expected Output="总结不同性别数量与比例的句子。"；Agent=DatabaseAnalystAgent；Context=前序任务的执行结果与语境（用于解析“这些用户”）<br>2) 组建并启动 Crew：同一 Agent + 新 Task（带上下文）<br>3) Agent 利用上下文执行：识别需用 query_database<br>- Tool Call：query_database(query="SELECT gender, COUNT(*) FROM users GROUP BY gender;")<br>- Tool 返回(JSON)：{"result":[{"gender":"male","count":6200},{"gender":"female","count":6145}]}<br>4) 综合并返回最终答案：LLM 基于详细数据生成总结<br>- 最终答案：好的，用户性别比例如下：男性用户有 6200 人，女性用户有 6145 人，比例接近 1:1。<br>5) 任务完成：返回最终分析结果 |

