# <PERSON><PERSON><PERSON><PERSON> vs LangGraph 技术调研对比

日期：2025-08-26

## 快速结论
- LangChain：通用 LLM 应用框架与组件库，擅长快速拼装 RAG/工具调用/流水线，生态最全，学习曲线低-中。
- LangGraph：在 LangChain/LCEL 之上提供“图式、可恢复、可观测”的状态化智能体/工作流，更适合复杂多步/多人在回路/长流程任务。
- 选型：简单/中等复杂度优先 LangChain；需要显式控制流、检查点恢复、人机协作、长时间运行优先 LangGraph。

## 对比表

| 维度 | LangChain | LangGraph |
|---|---|---|
| 核心定位 | 通用 LLM 应用框架与组件库；LCEL 流水线与 Agent 执行器 | 基于图的状态化智能体/工作流库；构建可恢复的多步/多体系统 |
| 适用场景 | 单/多轮对话、RAG、工具调用、轻量 Agent | 复杂工作流、长时间运行、多人/多体协作、强状态/恢复需求 |
| 编排模型 | 链/流水线（LCEL）、简单控制流 | 节点-边有向图，原生分支/循环/并发，显式控制流 |
| 状态管理 | 轻量上下文与记忆，持久化需自配 | 内置状态对象与 Checkpointer；可持久化/回溯/局部重放 |
| 错误处理/恢复 | 异常处理、重试器、回退策略 | 节点级错误边界、重试、从检查点恢复、局部重放 |
| 并发与分支 | 支持并行调用，缺少图级原语 | 原生分支、合并与并发步 |
| 人在回路 (HITL) | 需自定义实现 | 原生中断/等待外部输入，易实现 HITL |
| 可观测性/调试 | 日志/回调/追踪（如 LangSmith） | 与 LangSmith 深度集成；按节点/边观测、回放与时间旅行 |
| 生态与兼容 | 模型/工具/检索/存储适配最丰富 | 构建在 LangChain/LCEL 之上，天然复用 LC 生态 |
| 学习曲线 | 低-中 | 中-高（需要图式思维与状态建模） |
| 性能/成本 | 简单流程开销低；依赖开发者优化 | 显式控制与增量执行，适合长流程成本优化 |
| 部署/运行时 | 任意 Python/JS 环境，服务化需自封装 | 适合服务化/持久化运行；易与队列/分布式集成 |
| 缓存/重用 | 支持调用级缓存、检索缓存 | 结合检查点支持增量重用与局部重放 |
| 典型用例 | RAG 问答、函数调用助手、批处理转换 | 多工具多步 Agent、工单自动化、审批/对齐流 |
| 何时选择 | 需求简单、无强状态/恢复、重生态 | 明确工作流、可恢复、HITL、复杂协作/长流程 |

## 选型建议
- 快速 MVP、标准 RAG/函数调用：LangChain。
- 需要状态可恢复、审计回放、人机协作、复杂编排：LangGraph（并复用 LangChain 组件）。
- 团队新人多/交付周期紧：先 LangChain；演进到复杂场景再引入 LangGraph 图式编排。