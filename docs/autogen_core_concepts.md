# AutoGen 基础概念速览（多智能体对话式编排）

本文面向首次接触 AutoGen 的工程师/产品/数据同学，系统化梳理 AutoGen 的核心概念、协作机制与最小上手示例，帮助你在实践中快速落地。

---

## 1. AutoGen 是什么

- 设计哲学：用“对话”来编排多个智能体（Agent）协作，所有能力（理解、决策、调用工具、修正、定稿）都通过消息交互完成。
- 典型闭环：Assistant 生成方案或“可执行代码” → 交给执行器（user_proxy）执行 → 结果再反馈给 Assistant → 继续思考或收尾。
- 适用场景：探索式任务、多角度审查、需要“生成-执行-再总结”的回路（如代码执行、数据查询、工作流拼装）。

---

## 2. 核心概念

### 2.1 Agent（智能体）
AutoGen 的最小协作单元，具备角色设定、上下文记忆与回复能力。
- 常见类型：
  - AssistantAgent：由 LLM 驱动，负责“理解/推理/生成文字或代码”。
  - UserProxyAgent（简称 user_proxy）：“执行器 + 转发员”，负责接收用户输入、执行代码/函数、把结果回传给其他 Agent。
  - ConversableAgent：通用可对话基类，多数高级用法基于它扩展。
  - GroupChatManager：在多 Agent 协作时负责发言顺序与终止条件。
- 关键配置：
  - system_message：角色职责/风格/边界（如“你是合规审查员，只做合规检查”）。
  - llm_config：模型与推理参数（model、temperature、max_tokens 等）。
  - code_execution_config（user_proxy）：代码执行工作目录、沙箱、是否用 Docker 等。

### 2.2 Message（消息）与对话历史
- 组成：role（user/assistant/system）+ content（文本/代码/结构化信息）。
- 作用：所有 Agent 都持有各自的对话历史；下一轮响应基于“历史 + 新消息”。
- 重要性：历史就是上下文记忆，承载“前后语义关联”和“工具输出”。

### 2.3 Tool / Function（工具/函数）与代码执行
- 工具注册：在对话开始前，把可用的“函数”注册给 user_proxy（执行端集中管理权限/凭证）。
- 调用路径（常见范式）：
  1) Assistant 生成调用这些函数的 Python 代码；
  2) user_proxy 识别到代码块后执行；
  3) 将返回值作为消息回传给 Assistant；
  4) Assistant 基于结果生成最终答案或继续迭代。
- 安全控制：Docker 沙箱、函数白名单、资源限额、环境隔离、幂等键、防注入等策略集中在 user_proxy 侧。

### 2.4 GroupChat 与 GroupChatManager（多 Agent 协作）
- GroupChat：把多个 Agent 拉到一个“群聊房间”，共享消息历史。
- GroupChatManager：决定“谁是下一位发言者”“何时终止对话”“如何仲裁冲突”。
- 发言顺序（speaker_selection）：
  - round_robin：严格轮流；auto：基于内容自动选择；也可自定义函数。
- 终止条件（termination_condition）：
  - 轮数上限（max_round），或自定义结束判定（如“出现【审查通过】”或“拿到部署 ID”）。

### 2.5 Human-in-the-loop（人在回路）
- 模式：human_input_mode=ALWAYS/NEVER/TERMINATE（是否在关键节点询问人类）。
- 目的：把“人类审批/补充信息/最终拍板”自然融入对话式流程（如提交生产前强制确认）。

### 2.6 Prompt 设计与配置
- system_message：定义 Agent 的角色、目标、输出结构、边界条件。
- 内容模板：明确结构化输出 Schema、错误处理规范、必须调用的工具顺序等。
- 关键技巧：让 Agent 在输出前自检（checklist），遇到信息缺失先发“澄清问题”。

### 2.7 常见协作模式（Patterns）
- 单 Agent：快速草稿或简单任务（“自然语言 → JSON”）。
- 多 Agent 互审：Designer 生成 → Reviewer/QA 审查 → 修正 → 终稿。
- 规划-执行（Planner-Executor）：Planner 规划步骤，Executor 按步骤执行工具。
- 批评-改写（Critic-Refiner）：Critic 挑错与给建议，Refiner 迭代优化。
- 管理者（Orchestrator/Manager）：选择发言者、控制终止、调解冲突。

### 2.8 安全与风控
- 执行端隔离：使用 Docker/虚拟环境、禁用网络或限制出网、CPU/内存限额。
- 凭证与敏感信息：凭证管理放在 user_proxy，输出前做脱敏；
- 合规审计：记录工具调用与关键决策日志，便于回溯与审计。

---

## 3. 最小上手示例

> 以下为伪代码/思路骨架，帮助你搭建最小可运行原型。

### 3.1 单 Agent 生成结构化 JSON 草稿（纯文本）
```python
from autogen import AssistantAgent

canvas_agent = AssistantAgent(
    name="CanvasGenerator",
    system_message="""
你是营销流程画布设计专家。根据用户描述，直接输出结构化 JSON：
{
  "metadata": {"name": "...", "frequency_cap": {"per_user_per_day": 1}},
  "nodes": [{"id": "...", "type": "...", "params": {...}}],
  "edges": [{"from": "...", "to": "...", "condition": {...}}]
}
信息不足先澄清，确保拓扑合理、参数完整。
"""
)

reply = canvas_agent.generate_reply([
    {"role": "user", "content": "为新注册用户设计激活流程：24小时短信，48小时站内信，每日最多1次"}
])
print(reply)
```

### 3.2 单 Agent + 工具调用：生成 → validate → submit
```python
from autogen import AssistantAgent, UserProxyAgent

# 工具定义（由 user_proxy 执行）
def validate_canvas(canvas_json: str) -> dict:
    return {"ok": True, "errors": []}

def submit_canvas(canvas_json: str, env: str = "sandbox") -> dict:
    return {"deployment_id": "dp_123", "preview_url": "https://..."}

user_proxy = UserProxyAgent(
    name="user_proxy",
    code_execution_config={"work_dir": ".", "use_docker": False}
)
user_proxy.register_function(validate_canvas)
user_proxy.register_function(submit_canvas)

agent = AssistantAgent(
    name="CanvasAgent",
    system_message="""
工作流程：
1) 生成画布 JSON，调用 validate_canvas 校验；
2) 若有错误则修正再校验；
3) 询问用户后，调用 submit_canvas 提交到沙箱。
输出使用 Python 代码块调用上述函数。
"""
)

# 从 user_proxy 发起对话（会执行 agent 生成的代码块）
user_proxy.initiate_chat(
    agent,
    message="请设计新用户激活流程：注册后发短信，24小时后发站内信，每日最多1次"
)
```

---

## 4. 最佳实践建议

- 渐进式引入：先“单 Agent 纯文本产出”，再加入“工具调用”，最后再上“多 Agent 互审”。
- 明确输出 Schema：要求 Agent 严格输出 JSON/表格等结构化产物；输出前自检。
- 设定终止条件：限制轮数与成本；定义“通过/终止”的判定准则（如校验通过、用户确认）。
- 工具最小可信集合：把所有外部操作封装成安全可审计的函数，集中在 user_proxy 管理。
- 人在回路：提交生产、删除资源、涉及隐私的数据导出等，务必加人工确认。
- 观测与日志：保留对话历史、工具调用日志、关键决策，便于复盘与合规。

---

## 5. 常见问题（FAQ）

**Q1：为什么 Assistant 不直接执行工具？**
A：安全与可控。AutoGen 倾向“Assistant 只生成代码，执行由 user_proxy 负责”，将权限与审计收口，避免模型直接触达外部世界。

**Q2：历史过长会“遗忘”吗？如何处理？**
A：会。建议引入历史裁剪/摘要，或把关键信息写入“工作记忆”结构，在发起新轮次时注入。

**Q3：什么时候需要多 Agent？**
A：当需要不同专业视角（产品/技术/合规）互审、或需要规划-执行、批评-改写等协作模式时再引入。简单“自然语言→JSON”先用单 Agent。

**Q4：如何降低出错率？**
A：要求结构化输出 + 校验工具；在 Prompt 中加入自检清单；对关键步骤进行“先校验后提交”的双重把关。

---

## 6. 参考链接

- AutoGen 官方文档：https://microsoft.github.io/autogen/
- 示例与教程（社区）：https://github.com/microsoft/autogen
- 相关对比：LangGraph / CrewAI（见本仓库 docs/multi_agent_frameworks_comparison.md）



---

## 附录：CrewAI 基础概念速览

### A.1 什么是 CrewAI
- 定位：任务导向的“智能体团队协作”框架，模拟真实团队的分工协作。
- 设计哲学：以 Task 为中心，Agent 作为“有角色与目标的执行者”，通过工具完成任务，按流程（顺序/并行/层级）推进。
- 典型闭环：根据任务目标 → Agent 选择/调用工具完成子任务 → 产出结果 →（可选）人工审批 → 汇总/交付。

### A.2 核心基础概念
- Agent（智能体）：带角色与目标的执行者
  - 关键属性：role（角色）、goal（目标）、backstory（背景）、tools（工具清单）、memory（记忆）、allow_delegation（是否允许委派）。
- Task（任务）：分配给某个 Agent 的工作单
  - 关键属性：description（描述）、expected_output（期望产出）、context（依赖/输入）、output_file（输出保存）、callback（回调/审批）。
- Crew（团队）：Agent + Task 的组合与编排
  - 通过 process 指定执行策略：sequential（串行）、parallel（并行）、hierarchical（层级/可拆派）。
- Tools（工具）：通过 @tool 装饰器或现成工具注册；Agent 会根据任务自动选择调用。
- Memory/Context（上下文）：任务之间通过 context 传递结果与状态。
- Human-in-the-loop（人在回路）：支持 human_input 或审批型 Task，将人工确认纳入流程。
- Prompt/配置：在 Agent 定义中明确角色提示、工具清单、边界与约束。

### A.3 最小上手示例（伪代码）
```python
from crewai import Agent, Task, Crew, Process
from crewai_tools import tool

# 定义一个工具（示例）
@tool
def query_users(sql: str) -> str:
    """查询用户数据"""
    return "{\"count\": 12345}"

# 定义 Agent（数据分析师）
analyst = Agent(
    role="数据分析师",
    goal="统计用户规模并给出洞察",
    backstory="你擅长使用SQL分析与报告撰写",
    tools=[query_users],
    allow_delegation=False
)

# 定义 Task（统计用户量）
count_task = Task(
    description="统计数据库中用户总量，并输出JSON结果",
    agent=analyst,
    expected_output="JSON包含字段: count",
)

# 组建 Crew 并执行（顺序执行）
crew = Crew(agents=[analyst], tasks=[count_task], process=Process.sequential)
result = crew.kickoff()
print(result)
```

### A.4 与 AutoGen 的关键区别（简要）
- 编排范式：CrewAI 偏“任务/团队导向”，AutoGen 偏“对话/消息导向”。
- 工具调用：CrewAI 中 Agent 直接选择并调用工具；AutoGen 常由 Assistant 生成代码、交由 user_proxy 执行。
- 流程控制：CrewAI 通过 Task 依赖 + 执行策略（串行/并行/层级）；AutoGen 通过 GroupChat 的发言顺序 + 终止条件。
- 状态管理：CrewAI 以任务上下文传递状态；AutoGen 以对话历史为主（可自扩展工作记忆）。
- 人在回路：CrewAI 提供 human_input/审批型 Task；AutoGen 通过 user_proxy 的交互或终止前询问实现。
