# Agent 开发框架与平台大全（表格汇总）

最后更新：2025-08-25

说明
- 覆盖开源库/框架、多智能体编排、可视化低代码平台、云端 Agent 服务
- 列：名称｜类型｜主要语言｜核心能力｜维护方｜许可｜链接｜备注/适用场景
- 许可标注为通用参考，请以各项目仓库为准

| 名称 | 类型 | 主要语言 | 核心能力 | 维护方 | 许可 | 链接 | 备注/适用场景 |
|---|---|---|---|---|---|---|---|
| LangChain | 库/框架（含 Agents） | Python/JS | LCEL 链/图编排；Agent 执行器（ReAct/函数调用等）；工具与数据连接器生态（向量库/数据库/云服务）；RAG（加载器/检索器/合并器）；内存与历史管理；结构化输出与解析器；回调/Tracing/评估；并发与流式；多模型/路由 | LangChain Inc. | MIT | https://github.com/langchain-ai/langchain | 生态完备，快速构建 PoC/生产 |
| LangGraph | Agent 状态机/图编排 | Python/JS | 确定性状态管理（DAG/循环）；检查点/可复现运行/回退与恢复；事件驱动与并行边；多 Agent/人类在环；重试/超时与容错；与 LangChain/LCEL 深度集成；长流程观测与调试 | LangChain Inc. | MIT | https://github.com/langchain-ai/langgraph | 适合稳定的多步/闭环 Agent |
| LlamaIndex | 框架（含 Agents） | Python/JS | 数据加载与分块/Node 抽象；多类索引与 Query Engine；RAG 管道（检索/合并/后处理）；Agent/Router/Selector；观测与评估；结构化输出；多模态与工具调用；向量/图/表数据连接 | LlamaIndex | MIT | https://github.com/run-llama/llama_index | 强检索与数据侧能力 |
| AutoGen | 多智能体框架 | Python | 多 Agent 群聊/对话图；函数/工具调用与代码执行；角色与协议；人类代理/确认环节；重试/回退与容错；成本/令牌控制；会话记忆；插件与示例场景（规划-执行/评审-合并） | Microsoft | MIT | https://github.com/microsoft/autogen | 多角色协作编排典型 |
| Semantic Kernel | 编排框架 | C#/Python/JS | Planner（任务拆解）与 Orchestrator；Skills/Plugins（函数工具）；记忆（向量/语义）；连接器（存储/消息/业务系统）；Prompt 模板与参数化；Azure 集成与安全；可观测/日志 | Microsoft | MIT | https://github.com/microsoft/semantic-kernel | 与 .NET/企业系统友好 |
| Haystack (Agents) | RAG/Agents 框架 | Python | 可组合 Pipeline 组件；RAG（索引/检索/阅读器/生成器）；Agent/工具；评估套件；文档存储与连接器；标注与反馈；REST Serving/部署脚手架 | deepset | Apache-2.0 | https://github.com/deepset-ai/haystack | 生产级检索与问答 |
| CrewAI | 多智能体编排 | Python | 角色/任务分配；流程与依赖；工具/函数调用；交接与协作；记忆与报告；评估与监督；团队式协作模式 | CrewAI | MIT | https://github.com/joaomdmoura/crewai | 团队协作/角色分工 |
| SuperAGI | Agent 平台/框架 | Python | 多 Agent 管理；工具市场与向量存储；工作流/计划与调度；监控/审计/日志；UI 控制台；模型路由与凭据管理；持久化与项目化管理 | SuperAGI | Apache-2.0 | https://github.com/SuperAGI/superagi | 自托管 Agent 平台 |
| AgentScope | 多智能体框架 | Python | 多 Agent 协作编排；实验与回放；可视化对话图；评估指标与数据集；监控与日志；大规模实验管理 | Alibaba DAMO | Apache-2.0 | https://github.com/alibaba/AgentScope | 多 Agent 研究/评测 |
| OpenAI Swarm | 轻量编排库 | Python | 极简多 Agent 协作；Agent 间切换/移交；函数分发；最小状态模式；易于快速原型与研究 | OpenAI | MIT | https://github.com/openai/swarm | 轻量/实验友好 |
| Phidata | Agent 应用框架 | Python | 快速应用脚手架；工具连接（SQL/检索/网页/文件）；会话状态与记忆；工作区与UI部件；流式/结构化输出；部署示例 | Phidata | MIT | https://github.com/phidatahq/phidata | 业务型 Agent 快速落地 |
| Camel-AI | 角色扮演多 Agent | Python | 角色/协议驱动协作；任务场景模板；对话约束与安全边界；协商/分工；研究与教学用例 | CAMEL-AI | MIT | https://github.com/camel-ai/camel | 研究/教学/对话协作 |
| smolagents | 轻量 Agent 库 | Python | 简洁工具/函数注册；安全代码执行沙箱；逐步行动与观察；图像/多模工具；少依赖、易集成 | Hugging Face | Apache-2.0 | https://github.com/huggingface/smolagents | 极简、小型任务 |
| Transformers Agents | Transformers 扩展 | Python | 基于模型的工具调用；文本/视觉多模态；Pipelines 集成；工具注册与调度；示例驱动 | Hugging Face | Apache-2.0 | https://huggingface.co/docs/transformers/agents | 贴合 HF 模型生态 |
| Langroid | Agent/RAG 框架 | Python | 多 Agent 协作与任务分解；协商/仲裁；RAG 连接与检索；会话状态与内存；并行与路由 | Langroid | MIT | https://github.com/langroid/langroid | 任务导向型协作 |
| MetaGPT | 多 Agent“软件公司” | Python | 角色化公司（PM/设计/开发/测试）；SOP/流程模板；PRD/设计/代码/测试文档生成；任务分派与合并；仓库与工件管理 | DeepWisdom/社区 | Apache-2.0 | https://github.com/geekan/MetaGPT | 软件工程流程仿真 |
| AutoGPT | 通用 Agent 项目 | Python | 目标拆解与循环规划；插件体系与工具扩展；持久化记忆；浏览/文件/执行命令；自主探索与纠偏；配置驱动 | 社区 | MIT | https://github.com/Significant-Gravitas/AutoGPT | 代表性通用 Agent |
| BabyAGI | 任务循环原型 | Python | 任务创建→执行→优先级循环；简易向量记忆；概念验证；最小可运行示例 | 社区 | MIT | https://github.com/yoheinakajima/babyagi | 教学/研究 |
| OpenDevin | 开发者 Agent | Python | 文件树/编辑/运行；终端与浏览器自动化；任务面板与规划；工具集成；安全控制与回放；评估基线 | OpenDevin | Apache-2.0 | https://github.com/OpenDevin/OpenDevin | 代码/软件开发代理 |
| TaskWeaver | 代码 Agent | Python | 代码计划→执行循环；工具路由与依赖管理；监控与可观测；沙箱执行；多步任务划分 | Microsoft | MIT | https://github.com/microsoft/TaskWeaver | 以代码为核心的执行 |
| Griptape | 企业工作流/Agent | Python | 任务/子任务/工具结构化；内存与策略；数据连接器；审计与合规；驱动抽象（模型/存储/队列）；工作流持久化 | Griptape | Apache-2.0 | https://github.com/griptape-ai/griptape | 企业流程与治理 |
| PydanticAI | Agent 库 | Python | 类型安全工具/消息/状态建模（Pydantic）；结构化输出校验；函数调用与重试；会话上下文；流式与日志；易测试 | Pydantic | MIT | https://github.com/pydantic/pydantic-ai | 强类型与校验友好 |
| Open-Interpreter | 执行型 Agent | Python | 自然语言→本地代码执行；多语言/终端/文件操作；人工确认；音频/语音交互；安全沙箱与权限提示 | OpenInterpreter | MIT | https://github.com/OpenInterpreter/open-interpreter | 本地自动化/数据处理 |
| Flowise | 可视化建构器 | TS/Node | 拖拽式 LLM/Agent/RAG 流程；节点市场与连接器；在线运行与调试；持久化/凭据管理；简单部署 | FlowiseAI | OSS | https://github.com/FlowiseAI/Flowise | 低代码可视化编排 |
| LangFlow | 可视化建构器 | Python | 可视化 LangChain/LangGraph 流程；节点调试与变量；模板与导出；快速原型与演示 | LangFlow | OSS | https://github.com/langflow-ai/langflow | 可视化/组件化 |
| Dify | LLM 应用/Agent 平台 | TS/Go | 工作流/Agent/RAG；插件生态；数据集管理与观测；团队空间与权限；应用托管与日志；评估/版本 | LangGenius | Apache-2.0 | https://github.com/langgenius/dify | 自托管/团队协作 |
| Agents for Amazon Bedrock | 云 Agent 服务 | 托管 | 托管式 Agent 编排；工具/API/Lambda 集成；Knowledge Bases（检索/知识库）；Guardrails（安全策略）；会话状态/记忆；监控与日志；与 AWS 服务深度集成 | AWS | 商用 | https://aws.amazon.com/bedrock/agents/ | 云原生/与 AWS 生态结合 |
| Vertex AI Agent Builder | 云 Agent 服务 | 托管 | 对话与搜索（原 Vertex AI Search/Conversation）；工具与数据源集成；Grounding/检索；安全与合规；评估与监控；与 GCP 数据/应用整合 | Google Cloud | 商用 | https://cloud.google.com/vertex-ai | 与 GCP 一体化 |
| Azure AI Agent Service (Preview) | 云 Agent 服务 | 托管 | 多 Agent 编排；工具/函数/检索；监控与度量；与 Azure OpenAI/认知搜索/存储等集成；安全与治理；企业网络与合规 | Microsoft Azure | 商用 | https://azure.microsoft.com | 预览期，面向企业 |
| OpenAI Assistants API | 平台 API | 托管 | 线程/运行抽象；工具：函数调用/检索/代码解释器；文件与向量存储管理；并行工具调用；流式与事件回调；易于托管“助手/Agent” | OpenAI | 商用 | https://platform.openai.com/docs/assistants | 托管式 Agent 能力 |
| AgentScope Studio | 可视化/评测 | Python | 多 Agent 设计/回放；对话图与轨迹；指标评估；数据集/任务管理；实验对比与可视化面板 | Alibaba DAMO | Apache-2.0 | https://github.com/alibaba/AgentScope | 与 AgentScope 配套 |
| AgentLabs（示例项） | 可视化/托管 | 多样 | 可视化编排与部署；连接器与监控；团队协作；托管运行（以厂商说明为准） | 社区/厂商 | - | https://agentlabs.ai/ | 低代码/平台化 |

提示
- “库/框架”：用于在本地代码中构建 Agent（可与工具/RAG等结合）
- “多智能体框架”：支持多个 Agent 的协作（规划者/执行者/评审者/路由等）
- “可视化建构器”：拖拽式搭建/运行/监控 Agent 流程
- “云 Agent 服务/平台 API”：云端托管的 Agent 编排与运行能力，适合快速上线与企业集成

补充说明
- 选择建议：本地快速 PoC 优先 LangChain/LangGraph、LlamaIndex、AutoGen、PydanticAI；多 Agent 协作尝试 AutoGen、CrewAI、AgentScope；可视化与团队协作选 Dify/Flowise/LangFlow；公有云深度集成选 AWS Bedrock Agents、Vertex AI Agent Builder、Azure AI Agent Service。
- 安全/合规：生产场景建议结合 Guardrails（如 NeMo Guardrails）、策略/审计、以及“知识支撑+引用”的 RAG 方案。
- 评估与观测：关注重现性（如 LangGraph 检查点）、可观测性（日志/回放）、以及自动评测基线。

## 分类汇总（按用途与形态）

| 类别 | 框架明细 | 说明/选择建议 |
|---|---|---|
| 通用编排/Agents 库与框架 | LangChain, Semantic Kernel, Langroid, Phidata, Griptape, PydanticAI | 以代码工程化构建 Agent 的主力栈；LangChain 生态完备；Semantic Kernel 适合 .NET/企业集成；Griptape 偏治理/审计/持久化工作流；PydanticAI 提供强类型/结构化输出校验；Langroid 依赖较轻、任务协商友好；Phidata 适合快速业务型应用脚手架。 |
| 状态机/图编排 | LangGraph | 确定性 DAG/循环、检查点/回放/回退、人类在环；适合多步闭环与可复现编排，建议作为核心 Orchestrator。 |
| RAG/数据框架 | LlamaIndex, Haystack | LlamaIndex 偏数据/索引/QueryEngine，适合“字段字典/口径/模板”的 Grounding；Haystack 偏可组合 Pipeline 与评测/Serving，适合将 RAG 当作独立管线运营。 |
| 多智能体协作框架 | AutoGen, CrewAI, AgentScope, OpenAI Swarm, Camel-AI, MetaGPT | 需要 Planner–Executor、生成–评审–合并等协作时选；AgentScope 偏研究评测与回放；Swarm 极简原型；Camel-AI 角色扮演与协议实验；MetaGPT 用于软件公司流程仿真。 |
| 自治/原型类 Agents | AutoGPT, BabyAGI | 自主循环范式的研究/教学与灵感来源；生产不建议作主干。 |
| 开发者/代码执行 Agent | OpenDevin, TaskWeaver, Open-Interpreter | 面向代码与工具的自动化（编辑/运行/终端/浏览器/本地执行）；用于开发自动化或需要执行环境的任务，不直接用于营销画布桥接。 |
| 可视化/低代码编排 | Flowise, LangFlow, Dify, AgentLabs | 拖拽式构建 LLM/Agent/RAG 流程；适合团队协作与快速演示/托管；产品形态若非拖拽，可作为内部运营或原型工具。 |
| 云服务/平台 API（托管） | Agents for Amazon Bedrock, Vertex AI Agent Builder, Azure AI Agent Service, OpenAI Assistants API | 若深度采用对应云，可快速集成工具/数据/安全治理；需权衡厂商锁定、成本与自托管自由度。 |
| 评测/回放/可观测 | AgentScope Studio | 多 Agent 设计/回放/指标评估与实验对比；用于构建可观测性与质量基线。 |
| 自托管 Agent 平台 | SuperAGI | 一体化自托管平台（工具市场、工作流、监控与治理、控制台），当你希望内建平台能力而非自行拼装时考虑。 |
