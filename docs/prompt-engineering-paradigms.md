# Prompt Engineering 范式大全（强化示例版）

最后更新：2025-08-25

说明
- 本版保留必要英文名词（CoT, ReAct, RAG, HyDE 等），强化“可一眼看懂”的示例。
- 每个范式统一结构：真实任务、输入、提示词、期望输出（片段）、要点。
- 第一部分给出15个高频范式的详例；第二部分对其余范式按相同结构补齐最小可用示例。

阅读指引
- 入门优先：Few-shot、Instruction、JSON 输出约束、CoT、RAG、ReAct。
- 生产关注：输出约束、Verification/LLM-as-a-Judge、Router、Planner-Executor、Guardrail/Constitutional。
- 多智能体（如 AutoGen）：Debate、Creator–Critic–Resolver、Planner-Executor、Router/Selector、Reflexion。

统一示例主题
- 围绕“从原始文档生成结构化洞察”的企业场景，穿插市场摘要、评论抽取、FAQ 归纳、合规模板、代码小任务等。

---

# 一、强化示例（15 个高频范式）

## 1) Few-shot Prompting（少样本提示）
- 真实任务：将产品评论转为结构化 JSON（含情感、要点、证据）。
- 输入："电池续航比上一代强很多，但充电发热有点明显。"
- 提示词：
```text
你是电商数据分析助手。模仿示例将评论转成 JSON。

示例1
输入："屏幕很亮，户外也清晰，就是边框有点宽。"
输出：{"sentiment":"mixed","aspects":[
  {"aspect":"display","polarity":"positive","evidence":"屏幕很亮，户外也清晰"},
  {"aspect":"design","polarity":"negative","evidence":"边框有点宽"}]}

示例2
输入："相机夜景稳定，自拍也自然。"
输出：{"sentiment":"positive","aspects":[
  {"aspect":"camera","polarity":"positive","evidence":"夜景稳定"},
  {"aspect":"camera","polarity":"positive","evidence":"自拍也自然"}]}

现在处理：
输入:"{{评论文本}}"
仅输出 JSON。
```
- 期望输出（片段）：
```json
{"sentiment":"mixed","aspects":[
  {"aspect":"battery","polarity":"positive","evidence":"续航比上一代强很多"},
  {"aspect":"charging","polarity":"negative","evidence":"充电发热有点明显"}]}
```
- 要点：用示例锁定字段/粒度/证据，稳定输出。

## 2) Instruction Prompting（指令式）
- 真实任务：100 字中性市场摘要，含 1-2 个量化指标。
- 输入：新闻段落（Q2 出货 +18%，均价 -5%）。
- 提示词：
```text
目标：≈100字中性摘要，含1-2个量化指标；不得虚构；结尾给“可能影响”。
输入：{{新闻}}
仅输出纯文本。
```
- 期望输出（片段）："Q2 出货同比约+18%，均价约-5%... 可能影响：促销与成本。"
- 要点：目标+约束+格式明确，减少漂移。

## 3) Output Schema / JSON Constrained（结构化输出约束）
- 真实任务：抽取合同要点并返回严格 JSON。
- 输入：合同节选。
- 提示词：
```text
仅输出符合 JSON Schema 的合法 JSON：
{ "type":"object","required":["party_a","party_b","period","fee","breach_terms"],"properties":{
"party_a":{"type":"string"},"party_b":{"type":"string"},"period":{"type":"string"},
"fee":{"type":"string"},"breach_terms":{"type":"array","items":{"type":"string"}}},
"additionalProperties":false }
文本：{{合同节选}}
```
- 期望输出（片段）：{"party_a":"…","party_b":"…","period":"…","fee":"…","breach_terms":["…"]}
- 要点：Schema 约束字段与类型，便于程序消费。

## 4) Chain-of-Thought (CoT，思维链)
- 真实任务：费用报销规则判定（机票/酒店/餐补上限）。
- 输入：具体金额与规则。
- 提示词：
```text
请逐步推理并给出最终判定：每项是否合规及原因，不合规则给修正建议。
输入：{{报账项与规则}}
```
- 期望输出（片段）："机票 合规；酒店 合规；餐补 不合规（建议按150）"
- 要点：显式“规则→推理→结论”。

## 5) Self-Consistency（自洽采样）
- 真实任务：算术/单位换算稳定化。
- 输入：A=120/h, B=90/h, 合计 6h。
- 提示词：
```text
请逐步计算：各自每小时→合计每小时→6小时总量。
```
- 期望输出（片段）："1260"
- 要点：外层多次独立采样+投票选优。

## 6) Least-to-Most（由浅入深分解）
- 真实任务：SQL 统计近30天复购占比。
- 输入：orders(user_id, order_date, …)。
- 提示词：
```text
分解子问题：定义复购→复购人数→活跃人数→占比，逐步给出并附 SQL 示例。
```
- 期望输出（片段）：SELECT user_id ... HAVING COUNT(*)>=2
- 要点：把复杂需求拆成可验证台阶。

## 7) Deliberate（深思提示）
- 真实任务：PostgreSQL vs. MySQL 选型。
- 输入：高一致性、读多写中、复杂查询多。
- 提示词：
```text
从 事务/索引与查询/生态/成本 各列≥2要点；最后给结论+理由。
```
- 期望输出（片段）："倾向 PostgreSQL，因复杂查询与一致性更匹配。"
- 要点：明确审思维度→结论。

## 8) ReAct（Reason + Act）
- 真实任务：用汇率工具判断预算是否足够。
- 输入：USD 500 ↔ 需 CNY 2800；工具 get_fx_rate(USD,CNY)。
- 提示词：
```text
Thought→Action:get_fx_rate[{"base":"USD","quote":"CNY"}]→Observation→Final Answer（含计算）。
```
- 期望输出（片段）："足够（按7.25约3625）"
- 要点：推理-行动-观察循环。

## 9) Tool/Function Calling（函数调用）
- 真实任务：抽取后写库（upsert_insight）。
- 输入：新闻文本。
- 提示词：
```text
若可提取 标题/摘要/标签，请调用 upsert_insight 并提供参数；否则直接回答。
```
- 期望输出（片段）：{"tool":"upsert_insight","arguments":{"title":"…","summary":"…","tags":["…"]}}
- 要点：明确“何时调用/传何参”。

## 10) PAL / Program-of-Thought（程序辅助推理）
- 真实任务：含税后折扣总价计算。
- 输入：单价 129.9，数量 12，税 6%，折扣 7.5 折。
- 提示词：
```text
请用 Python 计算总价（含税后再打折），仅输出数值，保留两位小数。
```
- 期望输出（片段）："1451.34"
- 要点：用代码承担计算降低错误。

## 11) Retrieval-Augmented Generation（RAG）
- 真实任务：基于 wiki 片段回答并标引用。
- 输入：[doc_12:3] 餐补上限 150/天；[doc_18:2] 机票须经济舱。
- 提示词：
```text
仅用以下上下文回答，句尾以 [doc_id:页] 引用；不足则答“信息不足”。
上下文：{{片段}} 问题：{{Q}}
```
- 期望输出（片段）："餐补150/天 [doc_12:3]；机票经济舱 [doc_18:2]"
- 要点：严格“只用上下文”。

## 12) HyDE（Hypothetical Document Embeddings）
- 真实任务：检索稀疏主题“隐私合规日志留存策略”。
- 输入：主题查询。
- 提示词：
```text
先生成200字“假想综述”（不引用），随后用其进行向量检索并以真实文档RAG作答。
```
- 期望输出（片段）："最终回答引自真实文档并带引用"
- 要点：假想文档只助检索，作答基于真文档。

## 13) Tree-of-Thought（ToT，思维树）
- 真实任务：新品发布会议程多方案择优。
- 输入：目标与约束。
- 提示词：
```text
生成3方案（技术/用户故事/媒体），按 覆盖/时间/风险 打分（1-5），选优并合并为最终议程。
```
- 期望输出（片段）："选择B并吸收A的技术展示。"
- 要点：多分支→评估→合并。

## 14) LLM-as-a-Judge（模型裁判/量表评测）
- 真实任务：评价摘要 A/B 并排名。
- 输入：A、B 与量表（准确性/覆盖度/清晰度）。
- 提示词：
```text
按3项分别1-5打分并给理由；给总分与排名（1=最佳）。
```
- 期望输出（片段）："B(14分) > A(11分)"
- 要点：量表+理由提高一致性。

## 15) Data Extraction（结构化抽取）
- 真实任务：发票 OCR 转 JSON。
- 输入：发票文本。
- 提示词：
```text
抽取：invoice_no,date,seller,buyer,items[]{name,qty,unit_price,amount},total_amount,tax_amount；未知用null；金额两位小数。
```
- 期望输出（片段）：{"invoice_no":"…","items":[…],"total_amount":199.00}
- 要点：字段清单+缺失处理=可用 ETL。

---

# 二、其余范式速查清单（统一结构版）

- Zero-shot Prompting（零样本）
  - 真实任务：30字内摘要；简单快速。
  - 输入：{{文本}}
  - 提示词：将下段文本压缩为≤30字摘要；不得虚构；仅输出摘要。文本：{{文本}}
  - 期望输出（片段）："……（≤30字）"
  - 要点：用长度/格式等硬约束弥补无示例的不确定性。

- One-shot Prompting（单样本）
  - 真实任务：模仿示例输出相同结构。
  - 输入：{{输入}}
  - 提示词：示例 输入→输出；现在处理：{{输入}}；仅输出与示例同结构与字段名。
  - 期望输出（片段）：结构与示例一致。
  - 要点：一个高质量示例即可锁定格式与语气。

- Role/Persona（角色/人设）
  - 真实任务：法务审阅合同条款。
  - 输入：{{合同}}
  - 提示词：你是资深法务。先列风险点（条款/原因），再给修订建议（替换文本）。文本：{{合同}}
  - 期望输出（片段）："风险：…；修订：…"
  - 要点：角色+流程+语气齐全可显著稳定风格。

- Context+Instruction+Input+Output Format（结构化）
  - 真实任务：基于提供知识回答并输出 JSON。
  - 输入：{{知识片段}} + {{问题}}
  - 提示词：上下文：{{知识}}；指令：回答问题；输出：{"answer":string,"citations":string[]}；问题：{{Q}}
  - 期望输出（片段）：{"answer":"…","citations":["doc1#p2"]}
  - 要点：CIIO 四块清晰（Context/Instruction/Input/Output）。

- Scratchpad（草稿推理）
  - 真实任务：有中间计算的题。
  - 输入：{{题目}}
  - 提示词：Scratchpad：列中间推导；最终答案：单行结论。题：{{题目}}
  - 期望输出（片段）："最终答案：…"
  - 要点：限制最终答案为单行，便于程序消费。

- CoT + Tool Use（思维链+工具）
  - 真实任务：需要计算器/检索/执行。
  - 输入：{{问题}}
  - 提示词：逐步推理；遇计算/事实请调用工具（记录 Thought/Action/Observation）；最后给答案。
  - 期望输出（片段）："Observation: … → Final Answer: …"
  - 要点：显式记录工具反馈支撑结论。

- Self-Ask（自提问分解）
  - 真实任务：开放问题拆解。
  - 输入：{{问题}}
  - 提示词：先列≤5个子问题并逐一回答；必要时用工具；最后整合为结论。
  - 期望输出（片段）："子问1…子答1… → 结论…"
  - 要点：控制子问题数量与覆盖关键不确定性。

- Graph-of-Thought（思维图）
  - 真实任务：多路径合成方案。
  - 输入：{{目标与约束}}
  - 提示词：生成3条方案；在关键节点合并与互相校正；输出合成方案与合并理由。
  - 期望输出（片段）："合成方案：…；合并理由：…"
  - 要点：说明节点/边含义与合并标准。

- Reflexion（自反思/记忆）
  - 真实任务：文案改进并沉淀经验。
  - 输入：{{草稿}}
  - 提示词：初稿：{{DRAFT}}；给自评清单（错误/改进）；输出修订稿；最后列3条可复用经验。
  - 期望输出（片段）："修订稿：…；经验：1)…"
  - 要点：输出“问题→修订→经验”的闭环。

- Active Prompting（主动求证）
  - 真实任务：需求不全先澄清。
  - 输入：{{任务描述（不完整）}}
  - 提示词：若缺关键信息，先提出≤3个澄清问题并等待用户回答。
  - 期望输出（片段）："问题1…问题2…"
  - 要点：问少而准，覆盖阻塞执行的关键参数。

- Socratic Prompting（苏格拉底）
  - 真实任务：教学引导。
  - 输入：{{主题}}
  - 提示词：围绕主题设计5个由浅入深的问题并附参考要点。
  - 期望输出（片段）："Q1:… A要点:…"
  - 要点：每问推进一小步构建理解。

- Debate / Self-Debate（辩论/自辩）
  - 真实任务：多立场评估。
  - 输入：{{命题}}
  - 提示词：A/B 分别阐述立场+论据；Judge 依据标准（准确/有据/完整）裁决与理由。
  - 期望输出（片段）："裁决：支持A，理由：…"
  - 要点：给清晰评价维度与权重。

- Constitutional Prompting（宪法式）
  - 真实任务：按原则纠偏。
  - 输入：{{草稿}} + 原则集
  - 提示词：原则：{{CONSTITUTION}}；请对照原则自评并修订，标出修改点。
  - 期望输出（片段）："修改点：… 修订稿：…"
  - 要点：原则可执行且可检查（可转化为清单）。

- Guardrail/Safety Prompting（安全护栏）
  - 真实任务：合规拒绝并给替代。
  - 输入：{{潜在违规请求}}
  - 提示词：若涉及敏感/违法，拒绝并给3条安全替代方案（不含敏感数据）。
  - 期望输出（片段）："无法协助… 替代：1)…"
  - 要点：拒绝语模板化+具体可落地替代。

- Constraint-Driven（约束驱动）
  - 真实任务：受限摘要（长度/语气/数据点）。
  - 输入：{{文本}}
  - 提示词：≤120字，中性语气，包含1个数据点，不引用外部来源。文本：{{文本}}
  - 期望输出（片段）："……（≤120字，含数据点）"
  - 要点：约束清晰、可检查，才可靠。

- Directional Stimulus（方向性刺激）
  - 真实任务：边界/反例检查。
  - 输入：{{问题}}
  - 提示词：请优先检查边界值与反例，再给结论（避免直接给答案）。
  - 期望输出（片段）："边界：… 反例：… 结论：…"
  - 要点：给方向不剧透答案。

- Prompt Chaining（提示链）
  - 真实任务：多阶段写作。
  - 输入：{{主题}}
  - 提示词：阶段1要点→阶段2草稿→阶段3审稿→阶段4定稿；每阶段输出供下一阶段使用。
  - 期望输出（片段）："阶段1要点：… 最终稿：…"
  - 要点：显式传递阶段产物字段。

- Meta-Prompting（元提示/提示改写）
  - 真实任务：让模型优化提示。
  - 输入：{{原提示}}
  - 提示词：改写提示，加入目标/约束/输出格式，并列出改进点清单。
  - 期望输出（片段）："改写版：… 改进点：…"
  - 要点：产出“新提示+变化说明”。

- Example-Selection / In-Context Retrieval（示例选择）
  - 真实任务：从样本库挑例构造 Few-shot。
  - 输入：{{当前输入}} + 示例库（控制层）
  - 提示词：基于嵌入相似度选3例（覆盖不同模式），用其构造 Few-shot 提示。
  - 期望输出（片段）："选中示例ID：[e12,e37,e44]"
  - 要点：相似度与多样性平衡。

- Auto-CoT（自动思维链示例）
  - 真实任务：自动生成各类题型的 CoT 样例。
  - 输入：题目集合（控制层）。
  - 提示词：按题型聚类；每类产1条高质量 CoT 示例；存入样例库。
  - 期望输出（片段）："ClusterA→CoT示例…"
  - 要点：示例质量优先于数量。

- Verification / Self-Critique（自校验/自批判）
  - 真实任务：按规则检验并修订。
  - 输入：{{草稿}} + {{RUBRIC/TESTS}}
  - 提示词：列未达标点→修正→重检；若仍失败，输出失败摘要与下一步建议。
  - 期望输出（片段）："修正项：… 通过：是/否"
  - 要点：引入可执行检查点/测试用例。

- Double Check / Cross-Check（双重复核）
  - 真实任务：两解对照取优。
  - 输入：{{需求}}
  - 提示词：独立生成方案A/B；列差异与取舍理由；给最终合并方案。
  - 期望输出（片段）："差异：… 最终：…"
  - 要点：保证A/B独立性与对照透明。

- Confidence Calibration（置信度校准）
  - 真实任务：报告不确定性。
  - 输入：{{答案草稿}}
  - 提示词：输出答案+置信度(0-100%)+关键假设+未知/风险。
  - 期望输出（片段）："置信度：70%；假设：…"
  - 要点：配套列出假设与未知点说明边界。

- Style Transfer / Tone Control（风格/语气）
  - 真实任务：为财务受众改写。
  - 输入：{{文本}}
  - 提示词：为财务受众、正式语气重写；保持事实不变、数据一致。
  - 期望输出（片段）："（正式风格改写）"
  - 要点：禁止引入新事实。

- Multi-Modal Grounding（多模态）
  - 真实任务：图表描述+2个数字。
  - 输入：{{图像引用}}
  - 提示词：描述趋势并给2个关键数字（注明单位）。
  - 期望输出（片段）："上升趋势；峰值=…，均值=…"
  - 要点：明确量纲与单位。

- EBNF/Regex Constrained Output（语法约束）
  - 真实任务：生成匹配正则的ID。
  - 输入：{{上下文}}
  - 提示词：输出必须匹配 ^[A-Z]{3}-[0-9]{6}$；若无法生成则输出"INVALID"。
  - 期望输出（片段）："ABC-123456"
  - 要点：给出明确正则/EBNF 与兜底。

- Safety Red-Teaming（对抗性安全测试）
  - 真实任务：受控压力测试。
  - 输入：{{策略与范围}}
  - 提示词：在范围内设计3个攻击变体，记录模型响应与拒绝理由（仅用于评估）。
  - 期望输出（片段）："变体1…拒绝理由…"
  - 要点：仅测试环境，遵守合规。

- Planner-Executor（规划-执行）
  - 真实任务：步骤化自动化。
  - 输入：{{总体任务}}
  - 提示词：Planner 输出步骤(输入/输出/工具)；Executor 逐步执行并记录偏差。
  - 期望输出（片段）："步骤1→结果…"
  - 要点：分工明确，可观测与可追踪。

- Router/Selector（路由/选择）
  - 真实任务：按类型路由到专用路径。
  - 输入：{{请求}}
  - 提示词：若数值题→CoT+计算器；事实题→RAG；其它→Few-shot。输出所选路径与结果。
  - 期望输出（片段）："路由：RAG → 答案：…"
  - 要点：定义判别规则表并可测试。

- Few-shot CoT（少样本思维链）
  - 真实任务：用带推理示例固化风格。
  - 输入：{{题目}}
  - 提示词：示例含 Reasoning；现在解：先Reasoning再Answer，保持同风格。
  - 期望输出（片段）："Reasoning:… A:…"
  - 要点：控制推理深度与篇幅。

- Programmatic Templates（参数化模板）
  - 真实任务：批量统一提示。
  - 输入：{{变量块}}
  - 提示词：{{ROLE}} 执行 {{TASK}}，约束 {{CONSTRAINTS}}，格式 {{FORMAT}}。
  - 期望输出（片段）："（符合模板的输出）"
  - 要点：模板版本化、灰度与监控。

- Few-shot with Counterexamples（反例启发）
  - 真实任务：避免常见误用。
  - 输入：{{文本}}
  - 提示词：坏例：错误与原因；好例：修正做法；请按好例处理输入并输出。
  - 期望输出（片段）："（符合修正规范的输出）"
  - 要点：精选“最常错”的反例对。

- Ask-for-More（请求更多信息）
  - 真实任务：需求澄清。
  - 输入：{{任务}}
  - 提示词：列出≤5个关键缺失信息并按优先级排序；等待回答后继续。
  - 期望输出（片段）："1) … 2) …"
  - 要点：聚焦阻塞执行的关键信息。

- Stepwise Verification with Tests（测试驱动）
  - 真实任务：先测后解。
  - 输入：{{规格}}
  - 提示词：先生成3条单测；再给实现以通过测试；若失败，列失败点与修正。
  - 期望输出（片段）："测试通过：3/3"
  - 要点：回归友好，便于复现。

- Explanation then Decision（先解释后决策）
  - 真实任务：打分与选择。
  - 输入：{{候选方案+标准与权重}}
  - 提示词：逐项打分与理由；汇总权重得分；给出决策。
  - 期望输出（片段）："方案B 8.6分，入选"
  - 要点：先理由后结论，审计透明。

- Role Triad: Creator–Critic–Resolver（三角角色）
  - 真实任务：高质量定稿。
  - 输入：{{题目}}
  - 提示词：Creator 出草稿；Critic 列问题与建议；Resolver 修订成最终稿。
  - 期望输出（片段）："最终稿：…"
  - 要点：严格区分三角色职责。

- Knowledge-Grounded with Citations（引用）
  - 真实任务：带来源回答。
  - 输入：{{片段}} + {{问题}}
  - 提示词：仅用来源作答并用 [n] 引用；无依据时答“信息不足”。
  - 期望输出（片段）："… [2]"
  - 要点：来源断供则拒绝臆测。

- Memory-Augmented（带记忆）
  - 真实任务：个性化答复。
  - 输入：{{用户画像记忆}} + {{问题}}
  - 提示词：若相关请遵循画像；如需更新记忆请先确认后再用。
  - 期望输出（片段）："（考虑偏好的回答）"
  - 要点：最小必要记忆原则。

- Curriculum/Scaffolded（课程式）
  - 真实任务：分级学习。
  - 输入：{{主题}}
  - 提示词：Level1 基础→反馈→Level2 进阶→反馈→Level3 综合；每级仅引入少量新概念。
  - 期望输出（片段）："Level1 结果：…"
  - 要点：小步快跑+反馈闭环。

- Calibrated Refusal + Alternatives（合规拒绝）
  - 真实任务：违规请求处置。
  - 输入：{{请求}}
  - 提示词：说明拒绝原因，并提供3条可行的安全替代方案。
  - 期望输出（片段）："无法协助… 替代：…"
  - 要点：建设性替代要可落地。

- Pseudo-code First（先伪代码）
  - 真实任务：先设计算法再实现。
  - 输入：{{问题}}
  - 提示词：先给伪代码（含复杂度），再给 Python 实现（简洁），最后给用例验证结果。
  - 期望输出（片段）："用例通过：…"
  - 要点：实现紧随伪代码结构。

- Unit-of-Work（工作单元化）
  - 真实任务：仅抽取日期。
  - 输入：{{文本}}
  - 提示词：单一目标：输出 YYYY-MM-DD 或 null；仅输出该值。
  - 期望输出（片段）："2025-08-25"
  - 要点：极度聚焦一个输出。

- Role + Tools + Safety Bundle（角色+工具+安全）
  - 真实任务：生产级问答。
  - 输入：{{任务}}
  - 提示词：角色：数据分析师；工具：SQL/检索；安全：不输出个人隐私；输出格式：JSON；任务：{{任务}}
  - 期望输出（片段）：{"result":"…","used_tools":["sql"]}
  - 要点：角色+工具+安全三者俱全。

提示：微软 AutoGen 等多 Agent 框架常组合使用 Debate、Creator–Critic–Resolver、Planner-Executor、Router/Selector、Reflexion、Tool/Function Calling、ReAct、RAG 等范式进行编排。

---

# 术语速览
- CoT：Chain-of-Thought，思维链
- ToT：Tree-of-Thought，思维树
- GoT：Graph-of-Thought，思维图
- RAG：Retrieval-Augmented Generation，检索增强生成
- HyDE：Hypothetical Document Embeddings，假想文档嵌入
- ReAct：Reason + Act，推理与行动交替

---

# 延伸阅读（选）
- Chain-of-Thought Prompting（Google Research, 2022）
- Self-Consistency Improves Chain of Thought Reasoning（2023）
- ReAct: Synergizing Reasoning and Acting（2022）
- Tree-of-Thoughts: Deliberate Problem Solving with LLMs（2023）
- PAL: Program-Aided Language Models（2023）
- Self-Ask with Search（2022）
- Reflexion: Language Agents with Verbal Reinforcement Learning（2023）
- Graph of Thoughts（2023）
- Skeleton-of-Thought（2023）
- Retrieval-Augmented Generation & HyDE（2022-2023）
- Constitutional AI / Safety Prompting（多来源）