# 面向“对话生成流程画布 JSON”的架构图（选型落地版）

最后更新：2025-08-25

目标
- 通过自然语言对话，生成“流程画布 JSON（CanvasDSL）”，并提交给现有 Java 系统渲染与运行。
- 同一套对话流程支撑“人群画像（PersonaDSL）/标签（TagDSL）”结构化产出。
- 满足工程化、强约束、可回放、可审计，与现有大数据栈（Kudu/HDFS/Impala/Kafka）集成。

主干选型（呼应图中组件）
- 编排/状态机：LangGraph
- 工具/生态：LangChain
- 强类型与输出校验：PydanticAI（Pydantic 模型）
- Grounding/RAG：LlamaIndex（字段字典/口径/标签/策略模板/合规规则）
- Java 侧：现有流程画布服务（Canvas Import/Run）
- 数据与总线：Impala/Kudu/HDFS、Kafka

---

## 1) 总体组件架构图

```mermaid
flowchart TD
  user[用户] --> ui["对话 UI（Web/业务前端）"]
  ui --> api["Python AI Orchestrator (REST/gRPC)"]
  api --> lg["LangGraph Orchestrator<br/>(确定性编排/检查点/回放)"]
  lg --> lc["LangChain Tools<br/>(函数/检索/SQL/Kafka/校验器)"]
  lg --> pai["PydanticAI 校验<br/>(强类型/自动重试/Schema 版本)"]
  lg --> rag["LlamaIndex 索引/查询引擎<br/>(字段字典/口径/标签/策略模板/合规规则)"]
  rag --> know["知识库/索引存储<br/>- 字段字典/口径<br/>- 标签定义<br/>- 策略模板<br/>- 合规与FAQ"]

  %% Optional Enhancements
  subgraph Optional Enhancements
    crew["Multi-Agent Collab<br/>(CrewAI/AutoGen)"]
    grip["Griptape<br/>(Governance/Workflow)"]
    hay["Haystack<br/>(RAG Pipeline/Eval, Alt)"]
  end

  lg --> crew
  lg --> grip
  lg --> hay
  hay --> know

  lc --- imp[(Impala/Kudu/HDFS)]
  lc --- kfk[(Kafka Topics)]
  
  api -->|CanvasDSL JSON| jcs["Java Canvas Service<br/>(画布导入/运行)"]
  jcs --> kfk
  jcs --> run[执行引擎/调度]

  subgraph Data Plane
    imp
    know
  end

  subgraph Event Bus
    kfk
  end

  classDef comp fill:#eef,stroke:#447,stroke-width:1px;
  classDef data fill:#efe,stroke:#484,stroke-width:1px;
  classDef bus fill:#fee,stroke:#944,stroke-width:1px;

  class api,lg,lc,pai,rag,jcs,run,crew,grip,hay comp
  class imp,know data
  class kfk bus
```

要点
- 只允许“经 Grounding 的上下文”参与决策；无依据则“信息不足+澄清”。
- 所有中间产物按 Pydantic 模型校验，失败最小更改重试；提交前全局规则校验。
- Kafka 作为事件总线贯通提交、运行与回传，便于审计/回放/指标。

---

## 2) 端到端时序图（从对话到运行）

```mermaid
sequenceDiagram
  participant U as 用户
  participant UI as 对话UI
  participant AI as Python AI Orchestrator
  participant LG as LangGraph(编排)
  participant RAG as LlamaIndex(索引/查询)
  participant VAL as PydanticAI(校验)
  participant JCS as Java Canvas Service
  participant K as Kafka
  participant MA as MultiAgent(CrewAI/AutoGen)
  participant GOV as Griptape(Governance)
  participant HAY as Haystack(RAG/Eval)

  U->>UI: 自然语言需求
  UI->>AI: /chat(payload)
  AI->>LG: 启动编排（新会话/检查点）
  LG->>LG: 槽位提取/意图解析（Clarify 若缺信息）

  opt 替代 RAG 管线（可选）
    LG->>HAY: 通过 Haystack 检索/管线
    HAY-->>LG: 相关片段/评测
  end

  LG->>RAG: 术语映射/口径与模板查询（Grounding）
  RAG-->>LG: 引用片段/映射结果

  LG->>LG: 生成画布草案（PlanCanvas）

  opt 协作复核（可选）
    LG->>MA: 发送草案进行生成/评审/合并
    MA-->>LG: 复核后的草案
  end

  LG->>VAL: 画布草案→CanvasDSL 校验（类型/取值/图规则）

  opt 治理/审计（可选）
    LG->>GOV: 提交治理/合规/审计检查
    GOV-->>LG: 审计/策略检查结果
  end

  VAL-->>LG: 校验OK / 错误与建议
  alt 校验失败
    LG->>LG: 最小修复/再次校验（限次重试）
  end
  LG->>AI: 预览摘要/变更点/引用
  AI-->>UI: 展示预览，征询确认
  UI->>AI: 用户确认/修改意见
  AI->>LG: 应用修改→最终 CanvasDSL
  LG->>JCS: /canvas/import(CanvasDSL)
  JCS-->>LG: canvas_id/version
  LG->>K: 生产事件（submitted/approved）
  JCS->>K: 运行事件（started/completed/metrics）
  K-->>AI: 消费事件用于回放/指标
  AI-->>UI: 返回最终状态与引用
```

---

## 3) LangGraph 状态机（高层）

```mermaid
stateDiagram-v2
  [*] --> Clarify
  Clarify: 收集目标/约束/必填槽位<br/>缺失则追问
  Clarify --> Ground: 槽位齐备
  Ground: 术语/字段/口径/模板对齐<br/>不足=信息不足
  Ground --> Plan
  Plan: 生成节点/拓扑草案（含Split/Join/Wait/ABTest）
  Plan --> Validate
  Validate: Pydantic 类型/取值校验<br/>全局图规则（无环/连通/分支覆盖）
  Validate --> HumanGate: 预览摘要/风险/引用
  HumanGate --> Plan: 需要修改
  HumanGate --> Submit: 确认通过
  Submit: 提交至 Java Import 接口<br/>记录 Kafka 事件
  Submit --> Done
  Validate --> Clarify: 信息不足/口径不明
  Submit --> Error: 合同/契约拒绝或接口失败
  Error --> Clarify: 回退并重新澄清
```

设计要点
- 每个状态落检查点（checkpoint），支持回放/重试/回退。
- 关键状态（Submit 前）允许“人类在环”。

---

## 4) 数据契约/DSL 关系图（示意）

```mermaid
classDiagram
  class CanvasDSL {
    +string version
    +string canvas_id
    +string description
    +Node[] nodes
    +Edge[] edges
    +Meta metadata
  }
  class Node {
    +string id
    +string type  // AudienceSelection/Tagging/Strategy/Split/Join/Wait/ABTest/End...
    +Params params
    +Meta metadata
  }
  class Edge {
    +string from
    +string to
    +string condition
    +int priority
  }
  class PersonaDSL {
    +string version
    +string name
    +Filter cohort_definition
    +Insight[] insights
    +Evidence[] evidence
  }
  class TagDSL {
    +string version
    +string name
    +Rule rule
    +string scope
    +int ttl
  }
  CanvasDSL "1" --> "*" Node
  CanvasDSL "1" --> "*" Edge
```

契约策略
- SemVer：DSL 带版本（如 v1.x）；新增字段尽量向后兼容。
- 严格校验：PydanticAI 在每一步强制 Schema 校验，提交前做全局图规则校验。
- 引用保真：Canvas 说明中保留“引用依据”（字段口径/模板来源）。

---

## 5) 部署与拓扑（逻辑）

```mermaid
flowchart LR
  subgraph Python Cluster
    API["AI Orchestrator<br/>(REST/gRPC)"]
    WORK["LangGraph Workers<br/>(可水平扩展)"]
    VAL[PydanticAI 校验]
    RAG[LlamaIndex 引擎]

    subgraph Optional Enhancements
      CREW["Multi-Agent Collab<br/>(CrewAI/AutoGen)"]
      GRIP["Griptape<br/>(Governance)"]
      HAY["Haystack<br/>(RAG Pipeline/Eval)"]
    end
  end

  subgraph Java Cluster
    JCS["Canvas Service<br/>(导入/运行)"]
  end

  subgraph Data/Infra
    VEC[(向量/索引存储)]
    IMP[(Impala/Kudu/HDFS)]
    KAFKA[(Kafka Cluster)]
  end

  API --> WORK
  WORK --> VAL
  WORK --> RAG
  WORK --> CREW
  WORK --> GRIP
  WORK --> HAY
  RAG --> VEC
  HAY --> VEC
  WORK --> IMP
  API -- CanvasDSL --> JCS
  JCS --> KAFKA
  API --> KAFKA
```

运维要点
- Python 与 Java 服务均可水平扩展；LangGraph Worker 池按吞吐弹性伸缩。
- Kafka 作为事件总线，贯穿提交、运行、指标与回放。

---

## 6) 边界与职责

- Python 侧（AI 编排域）
  - 对话态管理、澄清与确认；Grounding；规划与草案；校验与预览；提交与审计。
- Java 侧（画布域）
  - 接收 CanvasDSL；渲染/存储/运行；事件上报；与业务系统深度耦合的执行。
- 数据域
  - 字段字典/口径/标签/策略模板/合规则唯一真源；Impala/Kudu/HDFS 为数据探查与校验的事实来源。
- 治理域
  - 检查点/回放、日志与指标、权限与审计、变更与回滚。

---

## 7) 关键接口（名称与方向）

- Python（对外）
  - POST /chat（多轮对话/澄清）
  - POST /plan（生成/更新画布草案，返回预览摘要+引用）
  - POST /submit（提交最终 CanvasDSL）
  - GET /audit/:id（查看检查点/轨迹/引用）
- Python（对内工具，LangChain Tools）
  - tool.query_impala(sql, limit)
  - tool.kafka_publish(topic, payload)
  - tool.dict_lookup(key) / tool.template_fetch(name)
- Java（对 Python）
  - POST /canvas/import（body: CanvasDSL JSON → {canvas_id, version}）
- 事件（Kafka Topics）
  - canvas_events: submitted/approved/started/completed/failed/metrics

---

## 8) 风险控制点与策略

- 幻觉/错误映射
  - 仅基于 Grounding 文档回答；“信息不足”优先于臆测；引用来源随输出返回。
- JSON 漂移/解析失败
  - PydanticAI 校验+最小更改重试；提交前做全局规则校验。
- 兼容性与演进
  - DSL 版本化；在 Python→Java 间设置“transformer”层保证旧版兼容。
- 可回放/可观测
  - LangGraph 检查点；对话与决策链路全量审计；Kafka 事件贯通。
- 安全与合规
  - 字段/口径唯一真源；隐私字段遮蔽；生产/灰度隔离。

---

附注
- 图为逻辑视图，实际部署需结合网络域/权限/高可用（多 AZ）设计。
- 关键技术名仅作实现建议，保持框架可替换性（如 RAG 引擎/向量库/模型提供方）。