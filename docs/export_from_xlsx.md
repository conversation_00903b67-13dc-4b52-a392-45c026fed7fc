| 对话过程 | AssistantAgent | CompressedAssistantAgent | ReActAgent | RealQueryAgent | langgraph |\n|---|---|---|---|---|---|\n| user：你好 | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>reply：<br>你好，有什么可以帮助你的吗？ | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>reply：<br>你好，有什么可以帮助你的吗？ | Thinking:<br>Thought: 用户当前发送了一个‘你好’，...<br>Final Answer: 你好，有什么可以帮助你的吗？<br>memory：<br>不存储<br>reply：<br>你好，有什么可以帮助你的吗？ | Thinking:<br>reply：你好。<br>check：YES<br>reply：<br>你好。 | **第 1 步：图初始化和用户输入**<br>状态: `{"messages": [{"role": "user", "content": "你好"}]}`<br>↓<br>**第 2 步：进入 Agent 节点**<br>动作: 将状态构建成 Prompt 发送给 LLM。<br>LLM 应答: `{"content": "你好，有什么可以帮助你的吗？"}` (判断无需工具)<br>↓<br>**第 3 步：进入条件边**<br>动作: 检查 Agent 输出，未检测到 `tool_calls`。<br>决策: 流程结束 (END)。<br>↓<br>**第 4 步：输出结果**<br>返回最终答案：“你好，有什么可以帮助你的吗？” |\n| user：请帮我查询一下数据库中有多少用户 | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>请帮我查询一下数据库中有多少用户<br>assistant：好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？<br>reply：<br>好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？ | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>请帮我查询一下数据库中有多少用户<br>assistant：好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？<br>reply：<br>好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？ | Thinking:<br>Thought: 用户需要我查询数据库，并统计数据库中有多少用户，首先，我应该调用工具...<br>Action: CDPSqlTool<br>Action Input：{"sql": "select ..."}<br>Observation：<这里是数据查询结果><br>Thought：我已经查询完数据，从数据中可以看出用户总数为...<br>Final Answer: 数据库中的用户总数为10238<br>memory：<br>不存储<br>reply：<br>数据库中的用户总数为10238 | Thinking:<br>reply: 请查询数据库，并统计有多少用户<br>check: NO，原因是数据库不明确<br>reply: 请查询CDP数据库的 users 表，并统计用户总数<br>check：YES<br>reply：<br>请查询CDP数据库的 users 表，并统计用户总数 | **第 1 步：图初始化和用户输入**<br>状态: `{"messages": [{"role": "user", "content": "请帮我查询一下数据库中有多少用户"}]}`<br>↓<br>**第 2 步：进入 Agent 节点 (第一次)**<br>动作: 将状态构建成 Prompt 发送给 LLM。<br>LLM 应答 (决策): `{"tool_calls": [{"name": "sql_database", "args": {"query": "SELECT COUNT(*) FROM users;"}}]}`<br>↓<br>**第 3 步：进入条件边**<br>动作: 检测到 `tool_calls`。<br>决策: 流程走向 Tool 节点。<br>↓<br>**第 4 步：进入 Tool 节点**<br>动作: 执行 `sql_database` 工具。<br>工具执行结果: `"[(10238,)]"`<br>↓<br>**第 5 步：流程回到 Agent 节点 (第二次)**<br>动作: 将工具结果加入状态，再次调用 LLM。<br>LLM 应答 (最终答案): `{"content": "数据库中的用户总数为 10238。"}`<br>↓<br>**第 6 步：再次进入条件边**<br>动作: 未检测到 `tool_calls`。<br>决策: 流程结束 (END)。<br>↓<br>**第 7 步：输出结果**<br>返回最终答案：“数据库中的用户总数为 10238。” |\n| user: 这些用户的性别比例是多少？ | memory：<br>user：你好<br>assistant：你好，有什么可以帮助你的吗？<br>user: 请帮我查询一下数据库中有多少用户<br>assistant：好的，由于我不具备数据库查询能力，请你提供具体的数据给我好吗？<br>user: 这些用户的性别比例是多少？<br>assistant: 由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计<br>reply：<br>由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计 | memory（假设触发压缩）：<br>assistant：用户首先问好，然后问了数据库用户数问题，已回复由于能力不足无法查询<br>user: 这些用户的性别比例是多少？<br>assistant: 由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计<br>reply：<br>由于我没有数据库查询能力，所以我也无法统计性别比例，请把相关数据给我，我将为你统计 | Thinking:<br>Thought: 现在需要查询所有用户的性别比例，首先，我应该调用工具...<br>Action: CDPSqlTool<br>Action Input：{"sql": "select ..."}<br>Observation：<这里是数据查询结果><br>Thought：我已经查询完数据，从数据中可以看出用户总数为...<br>Final Answer: 男生人数为 500人，占比为 xx%，女生人数为500人，占比为 xx%，未知性别人数为 ...<br>memory：<br>不存储<br>reply：<br>男生人数为 500人，占比为 xx%，女生人数为500人，占比为 xx%，未知性别人数为 ... | Thinking:<br>reply: 请查询CDP数据库的 users 表，并统计这些用户的性别比例<br>check: YES<br>reply：<br>请查询CDP数据库的 users 表，并统计这些用户的性别比例 | **第 1 步：图初始化和用户输入**<br>状态: `{"messages": [..., {"role": "user", "content": "这些用户的性别比例是多少？"}]}` (包含历史对话)<br>↓<br>**第 2 步：进入 Agent 节点 (第一次)**<br>动作: 将包含历史记录的状态构建成 Prompt 发送给 LLM。<br>LLM 应答 (决策): `{"tool_calls": [{"name": "sql_database", "args": {"query": "SELECT gender, COUNT(*) FROM users GROUP BY gender;"}}]}`<br>↓<br>**第 3 步：进入条件边**<br>动作: 检测到 `tool_calls`。<br>决策: 流程走向 Tool 节点。<br>↓<br>**第 4 步：进入 Tool 节点**<br>动作: 执行 `sql_database` 工具。<br>工具执行结果: `"[('male', 500), ('female', 500), ...]"`<br>↓<br>**第 5 步：流程回到 Agent 节点 (第二次)**<br>动作: 将工具结果加入状态，再次调用 LLM。<br>LLM 应答 (最终答案): `{"content": "男生人数为 500人，占比为 xx%，女生人数为500人，占比为 xx%..."}`<br>↓<br>**第 6 步：再次进入条件边**<br>动作: 未检测到 `tool_calls`。<br>决策: 流程结束 (END)。<br>↓<br>**第 7 步：输出结果**<br>返回最终答案：“男生人数为 500人，占比为 xx%...” |\n