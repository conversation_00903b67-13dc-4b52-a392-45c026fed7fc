# LangGraph 对话分析

## 对话一: "你好"

### 对话过程
> **User:** 你好

### langgraph 处理细节

**1. 定义 Agent 和 Tools**
首先，定义一个可以与用户交互的 Agent，并为其提供一个工具集。在这个例子中，我们可能不需要特定的工具，但一个基本的对话 Agent 是必需的。

**2. 初始化 Graph**
创建一个 `StatefulGraph` 实例。这个 state 将包含对话历史，以便 Agent 能够记住之前的交流。
```json
{
  "messages": ["Human: 你好"]
}
```

**3. 定义 Graph 节点**
创建一个 "call_model" 节点。这个节点会调用 LLM 来生成回应。它接收当前的 state（包含消息）作为输入。

**4. 构建 Graph 流程**
设置 `entry_point` 为 "call_model" 节点，并将 "call_model" 的输出连接回图的末尾。这是一个简单的循环，允许持续的对话。

**5. LLM 调用**
- **Prompt:**
  ```
  System: You are a helpful assistant.
  User: 你好
  ```
- **LLM 返回:**
  ```
  Assistant: 你好！有什么可以帮你的吗？
  ```

**6. 更新 State**
LLM 的回复被添加回 state 的 "messages" 列表中。
```json
{
  "messages": ["Human: 你好", "AI: 你好！有什么可以帮你的吗？"]
}
```

---

## 对话二: "请帮我查询一下数据库中有多少用户"

### 对话过程
> **User:** 请帮我查询一下数据库中有多少用户

### langgraph 处理细节

**1. State 传入**
包含“你好”对话历史的 state 和新问题“请帮我查询一下数据库中有多少用户”被传入 Graph。
```json
{
  "messages": ["Human: 你好", "AI: 你好！有什么可以帮你的吗？", "Human: 请帮我查询一下数据库中有多少用户"]
}
```

**2. Router (路由判断)**
Graph 的 `entry_point` 现在指向一个 "router" 节点。这个 router 使用一个带有函数调用能力的 LLM 来判断用户的意图。
- **Prompt:**
  ```
  System: You are an expert at routing a user question to a tool or directly to a final answer.
  You have a tool 'query_database' with the description 'Can be used to query the user database'.
  Based on the user's question, should you use a tool? If so, which one and with what arguments?

  User: 请帮我查询一下数据库中有多少用户
  ```
- **LLM 返回 (Tool Call):**
  ```json
  {
    "tool_calls": [{
      "name": "query_database",
      "arguments": {
        "query": "SELECT COUNT(*) FROM users;"
      }
    }]
  }
  ```

**3. 条件分支**
Router 的输出包含 `tool_calls`，因此 Graph 会沿着定义好的条件边（conditional edge）走向 "tool_node"。

**4. Tool Node (工具节点)**
"tool_node" 执行 `query_database` 工具，并传入参数 `{"query": "SELECT COUNT(*) FROM users;"}`。
- **Tool 返回:**
  ```json
  {
    "result": [{"count": 12345}]
  }
  ```

**5. LLM 调用 (生成最终答案)**
工具的输出被格式化并与原始问题一起传递给 "call_model" 节点，以生成自然语言回答。
- **Prompt:**
  ```
  System: You are a helpful assistant.
  User: 请帮我查询一下数据库中有多少用户
  Tool Output (query_database): {"result": [{"count": 12345}]}
  Based on the user's question and the tool's output, provide a natural language response.
  ```
- **LLM 返回:**
  ```
  Assistant: 数据库中共有 12345 位用户。
  ```

**6. 更新 State**
工具调用、工具结果和最终的 AI 回复都被添加回 state。

---

## 对话三: "这些用户的性别比例是多少？"

### 对话过程
> **User:** 这些用户的性别比例是多少？

### langgraph 处理细节

**1. State 传入**
包含之前所有对话历史的 state 和新问题“这些用户的性别比例是多少？”被传入 Graph。

**2. Router (路由判断)**
Router 再次被调用。由于对话历史的存在，LLM 能够理解“这些用户”指代的是数据库中的所有用户。
- **Prompt:**
  ```
  System: You are an expert at routing a user question to a tool or directly to a final answer.
  You have a tool 'query_database' with the description 'Can be used to query the user database'.
  Based on the user's question and the conversation history, should you use a tool? If so, which one and with what arguments?

  Conversation History:
  Human: 请帮我查询一下数据库中有多少用户
  AI: 数据库中共有 12345 位用户。

  User: 这些用户的性别比例是多少？
  ```
- **LLM 返回 (Tool Call):**
  ```json
  {
    "tool_calls": [{
      "name": "query_database",
      "arguments": {
        "query": "SELECT gender, COUNT(*) FROM users GROUP BY gender;"
      }
    }]
  }
  ```

**3. 条件分支与工具调用**
Graph 再次走向 "tool_node"，执行数据库查询。
- **Tool 返回:**
  ```json
  {
    "result": [
      {"gender": "male", "count": 6200},
      {"gender": "female", "count": 6145}
    ]
  }
  ```

**4. LLM 调用 (生成最终答案)**
工具结果被送回 "call_model" 节点。
- **Prompt:**
  ```
  System: You are a helpful assistant.
  User: 这些用户的性别比例是多少？
  Tool Output (query_database): {"result": [{"gender": "male", "count": 6200}, {"gender": "female", "count": 6145}]}
  Based on the user's question and the tool's output, provide a natural language response.
  ```
- **LLM 返回:**
  ```
  Assistant: 好的，用户性别比例如下：男性用户有 6200 人，女性用户有 6145 人，比例接近 1:1。
  ```

**5. 更新 State**
完整的交互过程再次被记录到 state 中，以备后续提问。
