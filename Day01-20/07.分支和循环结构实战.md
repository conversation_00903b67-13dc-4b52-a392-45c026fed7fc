## 分支和循环结构实战

通过前面两节课的学习，大家对 Python 中的分支结构和循环结构已经有了初步的认知。**分支结构和循环结构是构造程序逻辑的基础**，它们的重要性不言而喻，但是对于初学者来说这也是比较困难的部分。很多人对分支结构和循环结构的语法是能够理解的，但是遇到实际问题的时候又无法下手；**看懂别人的代码很容易，但是要自己写出类似的代码却又很难**。如果你也有同样的问题和困惑，千万不要沮丧，这只是因为你的编程之旅才刚刚开始，**你的练习量还没有达到让你可以随心所欲写出代码的程度**，只要加强编程练习，通过量的积累来产生质的变化，这个问题迟早都会解决的。

### 例子1：100以内的素数

> **说明**：素数指的是只能被 1 和自身整除的正整数（不包括 1），之前我们写过判断素数的代码，这里相当于是一个升级版本。

```python
"""
输出100以内的素数

Version: 1.0
Author: 骆昊
"""
for num in range(2, 100):
    is_prime = True
    for i in range(2, int(num ** 0.5) + 1):
        if num % i == 0:
            is_prime = False
            break
    if is_prime:
        print(num)
```

### 例子2：斐波那契数列

要求：输出斐波那契数列中的前 20 个数。

> **说明**：斐波那契数列（Fibonacci sequence），通常也被称作黄金分割数列，是意大利数学家莱昂纳多·斐波那契（Leonardoda Fibonacci）在《计算之书》中研究理想假设条件下兔子成长率问题而引入的数列，因此这个数列也常被戏称为“兔子数列”。斐波那契数列的特点是数列的前两个数都是 1，从第三个数开始，每个数都是它前面两个数的和。按照这个规律，斐波那契数列的前 10 个数是：`1, 1, 2, 3, 5, 8, 13, 21, 34, 55`。斐波那契数列在现代物理、准晶体结构、化学等领域都有直接的应用。

```python
"""
输出斐波那契数列中的前20个数

Version: 1.0
Author: 骆昊
"""

a, b = 0, 1
for _ in range(20):
    a, b = b, a + b
    print(a)
```

> **说明**：上面循环中的`a, b = b, a + b`表示将变量`b`的值赋给`a`，把`a + b`的值赋给`b`。通过这个递推公式，我们可以依次获得斐波那契数列中的数。

### 例子3：寻找水仙花数

要求：找出 100 到 999 范围内的所有水仙花数。

> **提示**：在数论中，水仙花数（narcissistic number）也被称为超完全数字不变数、自恋数、自幂数、阿姆斯特朗数，它是一个 $\small{N}$ 位非负整数，其各位数字的 $\small{N}$ 次方和刚好等于该数本身，例如： $\small{153 = 1^{3} + 5^{3} + 3^{3}}$ ，所以 153 是一个水仙花数； $\small{1634 = 1^{4} + 6^{4} + 3^{4} + 4^{4}}$ ，所以 1634 也是一个水仙花数。对于三位数，解题的关键是将它拆分为个位、十位、百位，再判断是否满足水仙花数的要求，这一点利用 Python 中的`//`和`%`运算符其实很容易做到。

```python
"""
找出100到999范围内的水仙花数

Version: 1.0
Author: 骆昊
"""
for num in range(100, 1000):
    low = num % 10
    mid = num // 10 % 10
    high = num // 100
    if num == low ** 3 + mid ** 3 + high ** 3:
        print(num)
```

上面利用`//`和`%`拆分一个数的小技巧在写代码的时候还是很常用的。我们要将一个不知道有多少位的正整数进行反转，例如将 12389 变成 98321，也可以利用这两个运算来实现，代码如下所示。

```python
"""
正整数的反转

Version: 1.0
Author: 骆昊
"""
num = int(input('num = '))
reversed_num = 0
while num > 0:
    reversed_num = reversed_num * 10 + num % 10
    num //= 10
print(reversed_num)
```

### 例子4：百钱百鸡问题

> **说明**：百钱百鸡是我国古代数学家张丘建在《算经》一书中提出的数学问题：鸡翁一值钱五，鸡母一值钱三，鸡雏三值钱一。百钱买百鸡，问鸡翁、鸡母、鸡雏各几何？翻译成现代文是：公鸡 5 元一只，母鸡 3 元一只，小鸡 1 元三只，用 100 块钱买一百只鸡，问公鸡、母鸡、小鸡各有多少只？

```python
"""
百钱百鸡问题

Version: 1.0
Author: 骆昊
"""
for x in range(0, 21):
    for y in range(0, 34):
        for z in range(0, 100, 3):
            if x + y + z == 100 and 5 * x + 3 * y + z // 3 == 100:
                print(f'公鸡: {x}只, 母鸡: {y}只, 小鸡: {z}只')
```

上面使用的方法叫做**穷举法**，也称为**暴力搜索法**，这种方法通过一项一项的列举备选解决方案中所有可能的候选项，并检查每个候选项是否符合问题的描述，最终得到问题的解。上面的代码中，我们使用了嵌套的循环结构，假设公鸡有`x`只，显然`x`的取值范围是 0 到 20，假设母鸡有`y`只，它的取值范围是 0 到 33，假设小鸡有`z`只，它的取值范围是 0 到 99 且取值是 3 的倍数。这样，我们设置好 100 只鸡的条件`x + y + z == 100`，设置好 100 块钱的条件`5 * x + 3 * y + z // 3 == 100`，当两个条件同时满足时，就是问题的正确答案，我们用`print`函数输出它。这种方法看起来比较笨拙，但对于运算能力非常强大的计算机来说，通常都是一个可行的甚至是不错的选择，只要问题的解存在就能够找到它。

事实上，上面的代码还有更好的写法，既然我们已经假设公鸡有`x`只，母鸡有`y`只，那么小鸡的数量就应该是`100 - x - y`，这样减少一个条件，我们就可以把上面三层嵌套的`for-in`循环改写为两层嵌套的`for-in`循环。循环次数减少了，代码的执行效率就有了显著的提升，如下所示。

```python
"""
百钱百鸡问题

Version: 1.1
Author: 骆昊
"""
for x in range(0, 21):
    for y in range(0, 34):
        z = 100 - x - y
        if z % 3 == 0 and 5 * x + 3 * y + z // 3 == 100:
            print(f'公鸡: {x}只, 母鸡: {y}只, 小鸡: {z}只')
```

> **说明**：上面代码中的`z % 3 == 0`是为了确保小鸡的数量是 3 的倍数。

### 例子5：CRAPS赌博游戏

> **说明**：CRAPS又称花旗骰，是美国拉斯维加斯非常受欢迎的一种的桌上赌博游戏。该游戏使用两粒骰子，玩家通过摇两粒骰子获得点数进行游戏。简化后的规则是：玩家第一次摇骰子如果摇出了 7 点或 11 点，玩家胜；玩家第一次如果摇出 2 点、3 点或 12 点，庄家胜；玩家如果摇出其他点数则游戏继续，玩家重新摇骰子，如果玩家摇出了 7 点，庄家胜；如果玩家摇出了第一次摇的点数，玩家胜；其他点数玩家继续摇骰子，直到分出胜负。为了增加代码的趣味性，我们设定游戏开始时玩家有 1000 元的赌注，每局游戏开始之前，玩家先下注，如果玩家获胜就可以获得对应下注金额的奖励，如果庄家获胜，玩家就会输掉自己下注的金额。游戏结束的条件是玩家破产（输光所有的赌注）。

```python
"""
Craps赌博游戏

Version: 1.0
Author: 骆昊
"""
import random

money = 1000
while money > 0:
    print(f'你的总资产为: {money}元')
    # 下注金额必须大于0且小于等于玩家的总资产
    while True:
        debt = int(input('请下注: '))
        if 0 < debt <= money:
            break
    # 用两个1到6均匀分布的随机数相加模拟摇两颗色子得到的点数
    first_point = random.randrange(1, 7) + random.randrange(1, 7)
    print(f'\n玩家摇出了{first_point}点')
    if first_point == 7 or first_point == 11:
        print('玩家胜!\n')
        money += debt
    elif first_point == 2 or first_point == 3 or first_point == 12:
        print('庄家胜!\n')
        money -= debt
    else:
        # 如果第一次摇色子没有分出胜负，玩家需要重新摇色子
        while True:
            current_point = random.randrange(1, 7) + random.randrange(1, 7)
            print(f'玩家摇出了{current_point}点')
            if current_point == 7:
                print('庄家胜!\n')
                money -= debt
                break
            elif current_point == first_point:
                print('玩家胜!\n')
                money += debt
                break
print('你破产了, 游戏结束!')
```

### 总结

分支结构和循环结构都非常重要，是构造程序逻辑的基础，**一定要通过大量的练习来达到融会贯通**。我们可以用上面讲的花旗骰游戏作为一个标准，如果你能够很顺利的完成这段代码，那么分支结构和循环结构的知识你就已经很好的掌握了。

