# -*- coding: utf-8 -*-

import sys
from datetime import datetime, timed<PERSON><PERSON>


def get_name_list():
  ll = []
  with open("./temp", "r") as f:
    for line in f:
      l = line.split(' ')
      ll.append(l)


def send(ll):
  tt = datetime.strptime("2022-03-09", "%Y-%m-%d")
  print("xxxxx {tt}".format(tt=tt))
  print("length=" + str(len(ll)))
  idx=0
  while tt < datetime.strptime("2022-06-01", "%Y-%m-%d"):

    value = []
    single = {}

    single["start"] = tt.strftime("%Y-%m-%d")
    # tt = tt + timedelta(days=1)
    single["end"] = tt.strftime("%Y-%m-%d")
    tt = tt + timedelta(days=1)
    single["duty_num"] = "营销触点"

    if idx >= len(ll):
      idx = 0
    name1=ll[idx]
    idx = idx+1

    single["name"] =name1
    print("idx=" + str(idx))


    value.append(single)
    print("值班信息：{v}".format(v=value))
    import json
    data = {"type": 8, "value": value}
    # print(data)
    # data = json.dumps(data, ensure_ascii=False)
    # print(data)
    import requests
    url = 'https://www.sensorsdata.cn/api/support/insert'
    headers = {"Content-Type": "application/json"}
    r = requests.post(url, headers=headers, json=data)
    print(r.text)

if __name__ == "__main__":
  ll = ['刘欢','郭建勋', '余公猛','宋柯','王体均','张发伟','杨盆', '刘君健']
  send(ll)
