# -*- coding: utf-8 -*-
# 触点值班排班脚本
import sys
from datetime import datetime, timedelta


def get_name_list():
    ll = []
    with open("./temp", "r") as f:
        for line in f:
            l = line.split(' ')
            ll.append(l)


def send(ll):
    tt = datetime.strptime("2022-08-16", "%Y-%m-%d")
    print("xxxxx {tt}".format(tt=tt))
    print("length=" + str(len(ll)))
    idx = 0
    while tt < datetime.strptime("2022-10-31", "%Y-%m-%d"):

        value = []
        single = {}

        single["start"] = tt.strftime("%Y-%m-%d")
        single["end"] = tt.strftime("%Y-%m-%d")
        single["duty_num"] = "营销触点"
        # if tt.isoweekday() == 7:
        #     name1 = '王体均'
        # else:
        if idx >= len(ll):
            idx = 0
        name1 = ll[idx]
        # if tt.isoweekday() != 6:
        idx = idx + 1
        single["name"] = name1
        # print("idx=" + str(idx))

        value.append(single)
        print("值班信息：{v}".format(v=value))
        import json
        data = {"type": 8, "value": value}
        # print(data)
        import requests
        url = 'https://www.sensorsdata.cn/api/support/insert'
        headers = {"Content-Type": "application/json"}
        r = requests.post(url, headers=headers, json=data)
        print(r.text)
        tt = tt + timedelta(days=1)


if __name__ == "__main__":
    # ll = ['余公猛', '宋柯', '王体均', '张发伟', '杨盆', '刘君健', '刘欢', '郭建勋']
    ll = [ '杨盆', '余公猛', '张发伟', '蒋自国', '王体均', '宋柯']
    send(ll)
