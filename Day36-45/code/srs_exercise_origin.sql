-- 查询所有学生信息

-- 查询所有课程名称及学分(投影和别名)

-- 查询所有女学生的姓名和出生日期(筛选)

-- 查询所有80后学生的姓名、性别和出生日期(筛选)

-- 查询姓”杨“的学生姓名和性别(模糊)

-- 查询姓”杨“名字两个字的学生姓名和性别(模糊)

-- 查询姓”杨“名字三个字的学生姓名和性别(模糊)

-- 查询名字中有”不“字或“嫣”字的学生的姓名(模糊)

-- 查询没有录入家庭住址的学生姓名(空值)

-- 查询录入了家庭住址的学生姓名(空值)

-- 查询学生选课的所有日期(去重)

-- 查询学生的家庭住址(去重)

-- 查询男学生的姓名和生日按年龄从大到小排列(排序)

-- 查询年龄最大的学生的出生日期(聚合函数)

-- 查询年龄最小的学生的出生日期(聚合函数)

-- 查询男女学生的人数(分组和聚合函数)

-- 查询课程编号为1111的课程的平均成绩(筛选和聚合函数)

-- 查询学号为1001的学生所有课程的平均分(筛选和聚合函数)

-- 查询每个学生的学号和平均成绩(分组和聚合函数)

-- 查询平均成绩大于等于90分的学生的学号和平均成绩

-- 查询年龄最大的学生的姓名(子查询)

-- 查询年龄最大的学生姓名和年龄(子查询+运算)

-- 查询选了两门以上的课程的学生姓名(子查询/分组条件/集合运算)

-- 查询学生姓名、课程名称以及成绩(连接查询)

-- 查询选课学生的姓名和平均成绩(子查询和连接查询)

-- 查询每个学生的姓名和选课数量(左外连接和子查询)
