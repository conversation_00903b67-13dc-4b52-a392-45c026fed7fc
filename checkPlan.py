import json
import pymysql
from hyperion_client.hyperion_inner_client.inner_config_manager import InnerConfigManager

host = InnerConfigManager.get_instance().get_mysql_master()
user = InnerConfigManager.get_instance().get_client_conf('sp', 'mysql')['user']
password = InnerConfigManager.get_instance().get_client_conf('sp', 'mysql')['password']

# 根据通道实例 ID 查受影响的计划、画布
def check_jar():
    db = pymysql.connect(host=host, port=3305, database='metadata', user=user, password=password, charset='utf8')
    cursor = db.cursor()

    custom_channel_instance_ids = []
    # 所有推送的自定义账号,使用时注释其中一行

    sql0 = "select distinct i.id from sf_project p, express_action_msg_channel_instance i , express_action_plugin_channel c where i.channel_id=c.id and i.project_id=p.id and c.channel_route in ('PUSH_GETUI','PUSH_JIGUANG') and i.configuration not like '%is_vip%' and state='CHANNEL_AUTHORIZED';"

    # 所有短信的自定义账号,使用时注释其中一行
    # sql0 = "select a.id from express_action_msg_channel_instance a inner join express_action_plugin_channel b on a.channel_id = b.id inner join express_action_plugin c on c.id = b.plugin_id where c.plugin_route = 'EXPRESS_ACTION_SMS_PLUGIN' and a.ext_id > 0;"
    cursor.execute(sql0)
    result = cursor.fetchall()
    if result is not None:
        for line in result:
            if line is not None:
                if line[0] is not None:
                    custom_channel_instance_ids.append(line[0])

    # 计划的发送数量
    sql1 = "select sti.organization_id,spm.plan_id,spm.strategy_unit_id,spm.count,spm.current_day,json_extract(sp.msg_content, '$[*].channel_id') from sf_plan sp inner join sf_plan_metrics spm on spm.plan_id=sp.id inner join sf_project spj on sp.project_id=spj.id inner join sf_tenant_info sti on sti.id=spj.tenant_id where sp.type = 'SIMPLE' and spm.phase = 'MSG_SEND' and spm.current_day > '2022-10-12' and spm.current_day < '2022-10-20';"
    cursor.execute(sql1)
    result = cursor.fetchall()
    if result is not None:
        for line in result:
            if line is not None:
                if line[5] is not None:
                    filter_custom_jar(line, set(custom_channel_instance_ids))

    # 老画布的发送数量
    sql2 = "select sti.organization_id,spm.plan_id,spm.strategy_unit_id,spm.count,spm.current_day,json_extract(sp.strategy_unit_list, '$[*].channel_id') from sf_plan sp inner join sf_plan_metrics spm on spm.plan_id=sp.id inner join sf_project spj on sp.project_id=spj.id inner join sf_tenant_info sti on sti.id=spj.tenant_id where sp.type = 'RICH' and spm.phase = 'MSG_SEND' and spm.current_day > '2022-10-12' and spm.current_day < '2022-10-20';"
    cursor.execute(sql2)
    result = cursor.fetchall()
    if result is not None:
        for line in result:
            if line is not None:
                if line[5] is not None:
                    filter_custom_jar(line, set(custom_channel_instance_ids))

    # 新画布的发送数量
    sql3 = "select sti.organization_id,spm.plan_id,spm.strategy_unit_id,spm.count,spm.current_day,json_extract(spc.component_info, '$.action_component_rule.channel_instance_id') from sf_plan sp inner join sf_plan_metrics spm on spm.plan_id=sp.id inner join sf_plan_component spc on sp.id = spc.plan_id and spc.type='ACTION' inner join sf_project spj on sp.project_id=spj.id inner join sf_tenant_info sti on sti.id=spj.tenant_id where sp.type = 'CANVAS' and spm.phase = 'MSG_SEND' and spm.current_day > '2022-10-12' and spm.current_day < '2022-10-20';"
    cursor.execute(sql3)
    result = cursor.fetchall()
    if result is not None:
        for line in result:
            if line is not None:
                if line[5] is not None:
                    filter_custom_jar2(line, set(custom_channel_instance_ids))

    db.close()


def filter_custom_jar(line, custom_channel_instance_ids):
    plan_channel_instance_ids = json.loads(line[5])
    for plan_channel_instance_id in set(plan_channel_instance_ids):
        if plan_channel_instance_id is not None and plan_channel_instance_id in custom_channel_instance_ids:
            print("org_id=%s,plan_id=%s,strategy_unit_id=%s,count=%s,current_day=%s,channel_instance_id=%s" % (
            line[0], line[1], line[2], line[3], line[4], plan_channel_instance_id))


def filter_custom_jar2(line, custom_channel_instance_ids):
    if line[5] in custom_channel_instance_ids:
        print("org_id=%s,plan_id=%s,strategy_unit_id=%s,count=%s,current_day=%s,channel_instance_id=%s" % (
        line[0], line[1], line[2], line[3], line[4], line[5]))


check_jar()