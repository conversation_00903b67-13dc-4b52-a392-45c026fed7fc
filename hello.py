#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import pickle

m = {"k1":1, "k2":2}

def choose(a, b=4):
    if a > b:
        return a
    else:
        return b


def mysum(*numbers):
    sum = 0
    for n in numbers:
        sum = sum + n
    return sum


def say():
    print(sys.argv)


if __name__ == '__main__':
    say()

with open("./t.txt", "a") as f:
    f.write("牛逼")

with open("./t.txt") as f:
    print(f.read())

b = open("./t.txt", "wb")
pickle.dump(m, b)