#%%
import pandas
#%%
frame = pandas.read_csv('USvideos.csv')
frame
#%%
# 喜欢数5W以上的视频
frame[frame.likes>50000]
#%%
# 喜欢数5W以上的视频的总和
frame[frame.likes>50000].likes.sum()
#%%
# 喜欢数Top10视频
frame.sort_values('likes', ascending=False)[:10]
#%%
frame2 = frame.drop(index=frame[frame.video_id.duplicated()].index)
# 喜欢数Top10视频
frame2.sort_values('likes', ascending=False)[:10]
#%%
# 根据频道分组查看每个频道视频数量
frame.groupby('channel_title').size()
#%%
# 找出视频数量最多的频道Top10
frame.groupby('channel_title').size().sort_values(ascending=False)[:10]
#%%
%matplotlib inline

import matplotlib.pyplot as plt
# 绘制饼图
top10 = frame.groupby('channel_title').size().sort_values(ascending=False)[:10]
top10.plot.pie(figsize=(20, 10), autopct='%.2f%%')
plt.title('Top 10 Channels')
#%%
# 将Series转成DataFrame
frame3 = frame.groupby('channel_title').size().sort_values(ascending=False)[:10].to_frame('video_count')
frame3
#%%
# 将数据写入CVS文件
frame3.to_csv('result.csv')
#%%
