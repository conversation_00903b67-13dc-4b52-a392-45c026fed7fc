#%% md
## 三大神器概述

### 热身练习

如下列表保存着本公司从2022年1月到12月五个销售区域（南京、无锡、苏州、徐州、南通）的销售额（以百万元为单位），请利用这些数据完成以下操作：

```python
sales_month = [f'{i:>2d}月' for i in range(1, 13)]
sales_area = ['南京', '无锡', '苏州', '徐州', '南通']
sales_data = [
    [32, 17, 12, 20, 28],
    [41, 30, 17, 15, 35],
    [35, 18, 13, 11, 24],
    [12, 42, 44, 21, 34],
    [29, 11, 42, 32, 50],
    [10, 15, 11, 12, 26],
    [16, 28, 48, 22, 28],
    [31, 40, 45, 30, 39],
    [25, 41, 47, 42, 47],
    [47, 21, 13, 49, 48],
    [41, 36, 17, 36, 22],
    [22, 25, 15, 20, 37]
]
```

1. 统计本公司每个月的销售额。
2. 统计本公司销售额的月环比。
3. 统计每个销售区域全年的销售额。
4. 按销售额从高到低排序销售区域及其销售额。
5. 统计全年最高的销售额出现在哪个月哪个区域。
6. 找出哪个销售区域的业绩最不稳定。
#%%
sales_month = [f'{i:>2d}月' for i in range(1, 13)]
sales_area = ['南京', '无锡', '苏州', '徐州', '南通']
sales_data = [
    [32, 17, 12, 20, 28],
    [41, 30, 17, 15, 35],
    [35, 18, 13, 11, 24],
    [12, 42, 44, 21, 34],
    [29, 11, 42, 32, 50],
    [10, 15, 11, 12, 26],
    [16, 28, 48, 22, 28],
    [31, 40, 45, 30, 39],
    [25, 41, 47, 42, 47],
    [47, 21, 13, 49, 48],
    [41, 36, 17, 36, 22],
    [22, 25, 15, 20, 37]
]
#%%
# 魔法指令 - %whos - 查看变量
%whos
#%%
print = 100
# TypeError: 'int' object is not callable
# print('hello')
#%%
# 魔法指令 - %xdel - 删除变量
%xdel print
#%%
# 1. 统计本公司每个月的销售额。
monthly_sales = []
for i, month in enumerate(sales_month):
    monthly_sales.append(sum(sales_data[i]))
    print(f'{month}销售额: {monthly_sales[i]}百万')
#%%
# 2. 统计本公司销售额的月环比。
for i in range(1, len(monthly_sales)):
    temp = (monthly_sales[i] - monthly_sales[i - 1]) / monthly_sales[i - 1]
    print(f'{sales_month[i]}: {temp:.2%}')
#%%
# 3. 统计每个销售区域全年的销售额。
arealy_sales = {}
for j, area in enumerate(sales_area):
    temp = [sales_data[i][j] for i in range(len(sales_month))]
    arealy_sales[area] = sum(temp)
    print(f'{area}: {arealy_sales[area]}')
#%%
# 4. 按销售额从高到低排序销售区域及其销售额。
sorted_keys = sorted(arealy_sales, key=lambda x: arealy_sales[x], reverse=True)
for key in sorted_keys:
    print(f'{key}: {arealy_sales[key]}')
#%%
# 5. 统计全年最高的销售额出现在哪个月哪个区域。
max_value = sales_data[0][0]
max_i, max_j = 0, 0
for i in range(len(sales_month)):
    for j in range(len(sales_area)):
        temp = sales_data[i][j]
        if temp > max_value:
            max_value = temp
            max_i, max_j = i, j
print(sales_month[max_i], sales_area[max_j])
#%% md
总体方差：
$$
\sigma^{2} = \frac{1}{N} \sum_{i=1}^{N}(x_{i} - \mu)^{2}
$$

样本方差：
$$
s^{2} = \frac{1}{n - 1} \sum_{i=1}^{n}(x_{i} - \bar{x})^{2}
$$
#%%
# 6. 找出哪个销售区域的业绩最不稳定。
import statistics as stats

arealy_vars = []
for j, area in enumerate(sales_area):
    temp = [sales_data[i][j] for i in range(len(sales_month))]
    arealy_vars.append(stats.pvariance(temp))
sales_area[arealy_vars.index(max(arealy_vars))]
#%% md
### 三大神器

1. numpy - Numerical Python - 核心是`ndarray`类型，可以用来表示N维数组，提供了一系列处理数据的运算、函数和方法。
2. pandas - Panel Data Set - 封装了和数据分析（加载、重塑、清洗、预处理、透视、呈现）相关的类型、函数和诸多的方法，为数据分析提供了一站式解决方案。它的核心有三个数据类型，分别是：`Series`、`DataFrame`、`Index`。
3. matplotlib - 封装了各种常用的统计图表，帮助我们实现数据呈现。
4. scipy - Scientific Python - 针对NumPy进行了很好的补充，提供了高级的数据运算的函数和方法。
5. scikit-learn - 封装了常用的机器学习（分类、聚类、回归等）算法，除此之外，还提供了数据预处理、特征工程、模型验证相关的函数和方法。
6. sympy - Symbolic Python - 封装了符号运算相关操作。
#%%
# 魔法指令 - %pip - 调用包管理工具pip
# %pip install numpy pandas matplotlib openpyxl
#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

plt.rcParams['font.sans-serif'].insert(0, 'SimHei')
plt.rcParams['axes.unicode_minus'] = False
#%%
np.__version__
#%%
pd.__version__
#%%
# 将嵌套列表处理成二维数组
data = np.array(sales_data)
data
#%%
# 沿着1轴求和（每个月的销售额）
data.sum(axis=1)
#%%
# 沿着0轴求和（每个区域的销售）
data.sum(axis=0)
#%%
# 总体方差
data.var(axis=0).round(1)
#%%
# 样本方差
data.var(axis=0, ddof=1).round(1)
#%%
# 构造DataFrame对象（处理二维数据）
df = pd.DataFrame(data, columns=sales_area, index=sales_month)
df
#%%
# 求和（默认沿着0轴）
df.sum()
#%%
# 排序
df.sum().sort_values(ascending=False)
#%%
# 求和（指定沿着1轴）
df.sum(axis=1)
#%%
# 计算月环比
df.sum(axis=1).pct_change()
#%%
df['合计'] = df.sum(axis=1)
df['月环比'] = df['合计'].pct_change()
df
#%%
# 渲染DataFrame
df.style.format(
    formatter={'月环比': '{:.2%}'},
    na_rep='------'
).bar(
    subset='合计'
).background_gradient(
    'RdYlBu', subset='月环比'
)
#%%
# 将DataFrame输出到Excel文件
df.to_excel('sales.xlsx', sheet_name='data')
#%%
# 魔法指令 - %config - 修改配置
# %config InlineBackend.figure_format = 'svg'
get_ipython().run_line_magic('config', 'InlineBackend.figure_format = "svg"')
#%%
# 绘制柱状图
plt.figure(figsize=(8, 4), dpi=200)
df.plot(ax=plt.gca(), kind='bar', y='合计', legend=False)
plt.xticks(rotation=0)
plt.savefig('aa.png')
plt.show()
#%% md
### 魔法指令
#%%
# 查看当前工作路径 - print working directory
%pwd
#%%
# 查看指定路径文件列表 - list directory contents
%ls
#%%
# 执行系统命令
%system date
#%%
# 保存运行过的代码
%save temp.py
#%%
# 加载文件内容到单元格
%load temp.py
#%%
# 统计代码执行时间
%timeit (1, 2, 3, 4, 5)
#%%
# 查看历史输入
%hist
#%%
# 查看魔法指令
%lsmagic
#%% md
### 获取帮助
#%%
np.random?
#%%
np.random.normal?
#%%
np.random.r*?