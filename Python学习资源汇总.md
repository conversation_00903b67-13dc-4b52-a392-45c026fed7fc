## Python学习资源汇总

最近有很多小伙伴在找 Python 的相关学习资源，给大家做一个汇总吧，大家就不需要到处打听了，而且网上的资源良莠不齐，给大家整理一些优质的资源，让大家少走弯路。温馨提示一下，下面的资源选一些适合自己的就行了，并非每个都值得学习和研究。

### Python学习教程

#### 图文教程

1. [《从零开始学Python》](https://www.zhihu.com/column/c_1216656665569013760)- 我自己在知乎创作的专栏，欢迎大家打卡学习
2. [《基于Python的数据分析》](https://www.zhihu.com/column/c_1217746527315496960)- 我自己在知乎创作的专栏，欢迎大家学习交流
3. [《说走就走的AI之旅》](https://www.zhihu.com/column/c_1628900668109946880)- 我自己在知乎创作的专栏，欢迎大家学习交流
4. [《Python - 100天从新手到大师》](https://github.com/jackfrued/Python-100-Days) - 我自己在 GitHub 分享的 Python 学习项目
5. [《Python 3教程》](https://www.runoob.com/python3/python3-tutorial.html)- 菜鸟教程上的 Python 课程，上面还有很多其他学习资源
6. [《Python教程》](https://liaoxuefeng.com/books/python/introduction/index.html)- 廖雪峰个人网站上的 Python 课程，上面还有一些其他学习资源

#### 视频教程

1. [《从零开始学Python》](https://space.bilibili.com/1177252794/lists/1222205)- 我自己分享在 Bilibili 的 Python 入门视频
2. [《快速上手Python语言》](https://www.zhihu.com/education/video-course/1491848366791700480)- 在知乎知学堂上传的一套之前讲课的随堂视频
3. [《Python进阶课程》](https://space.bilibili.com/1177252794/lists/4128173)- 我自己分享在 Bilibili 的 Python 进阶随堂视频
4. [《Python数据分析三剑客》](https://space.bilibili.com/1177252794/lists/502289)- 我自己分享在 Bilibili 的 Python 数据分析随堂视频
5. [《AI Python for Beginners》](https://www.deeplearning.ai/short-courses/ai-python-for-beginners/)- 吴恩达（Andrew Ng）老师的 Python 入门课
6. [《AI for Everyone》](https://www.deeplearning.ai/courses/ai-for-everyone/)- 吴恩达（Andrew Ng）老师的 AI 通识课
7. [《Deep Learning Specilizaiton》](https://www.deeplearning.ai/courses/deep-learning-specialization/)- 吴恩达（Andrew Ng）老师的深度学习专项课程
8. [《100 Days of Code: The Complete Python Pro Bootcamp》](https://www.udemy.com/course/100-days-of-code/) - Udemy 上很受欢迎的一整套 Python 课程（付费）
9. [《Python for Data Science and Machine Learning Bootcamp》](https://www.udemy.com/course/python-for-data-science-and-machine-learning-bootcamp/) - Udemy 上一套评分很高的数据科学课程（付费）
10. [《PyTorch: Deep Learning and Artificial Intelligence》](https://www.udemy.com/course/pytorch-deep-learning/) - Udemy 好评课程（付费）

> **说明**：吴恩达老师的课程在 YouTube 和 Bilibili 上也有很多人分享，YouTube 上面也有很多免费的 Python 课程曾经让我觉得受益匪浅。这些课程很多都是言简意赅、直击问题的，不像国内很多培训机构，动不动就分享七百集的课程或者八百G的学习资料，让很多人误以为点赞收藏就是学会。国内外各种学习平台也很多，有人喜欢 Udemy，有人喜欢 Coursera，我只是把我自己看过觉得不错的课程分享出来，大家可以根据需要自己去对应的平台查找，当然更重要的是有计划的学习起来。

#### 资源网站

1. [Python 官方网站](https://python.org) - 下载 Python 解释器、查看官方文档、了解社区动态等
2. [Online Python](<https://www.online-python.com/)、[Python-Fiddle](https://python-fiddle.com/)、[Google Colab](https://colab.research.google.com/) - 在线编写运行 Python 代码
3. [Coddy](https://coddy.tech) - 在线学习和练习 Python，有点像学外语的“多邻国”
4. [Django](https://www.djangoproject.com/)、[FastAPI](https://fastapi.tiangolo.com/) 官方网站 - Python 服务接口开发框架
5. [NumPy](https://numpy.org/)、[Pandas](https://pandas.pydata.org/)、[Matplotlib](https://matplotlib.org/stable/) 官方网站 - Python 数据分析三大神器
6. [Polars](https://pola.rs/) 官方网站 - pandas 的高性能替代方案
7. [CuPy](https://cupy.dev/)、[cuDF](https://github.com/rapidsai/cudf)、[RAPIDS](https://rapids.ai/) 官方网站 - 用 GPU 加速数学科学
8. [Scikit-learn](https://scikit-learn.org/stable/)、[PyTorch](https://pytorch.org/)、[Tensorflow](https://www.tensorflow.org/)、[Keras](https://keras.io/)官方网站 - 机器学习、深度学习框架
9. [Hugging Face](https://huggingface.co/) 官方网站 - transformers 库提供了大量的预训练模型，助力深度学习模型开发
10. [Kaggle](https://www.kaggle.com/) 官方网站 - 全球知名的数据科学和机器学习平台
11. [GitHub](https://github.com/) 官方网站 - 全球最大代码托管平台，上面有很多优质的代码和资源
12. [YouTube](https://www.youtube.com/) 网站 - 全球最大的视频分享平台，有很多很棒的学习视频
13. [Bilibili](https://www.bilibili.com/) 网站 - 原本是一个二次元平台，现在也是年轻人的学习平台
14. [中国大学 MOOC](https://www.icourse163.org/)、[网易云课堂](https://study.163.com/)、[慕课网](https://www.imooc.com/)、[Udemy]()、[Coursera]()、[Udacity]()、[edX](https://www.edx.org/) - 在线学习平台
15. [DeepLearning.ai](https://www.deeplearning.ai/) - 吴恩达（Andrew Ng）老师创办的深度学习教育平台
16. [力扣](https://leetcode.cn/)、[牛客网](https://www.nowcoder.com/)、[HackerRank](https://www.hackerrank.com/)、[topcoder](https://www.topcoder.com/) - 在线刷题、比赛平台
17. [NVIDIA深度学习培训中心](https://link.zhihu.com/?target=https%3A//www.nvidia.cn/training/)、[DataCamp](https://link.zhihu.com/?target=https%3A//www.datacamp.com/) - 有免费和付费的学习资源
18. [BecomingHuman.ai](https://link.zhihu.com/?target=https%3A//becominghuman.ai/) - 上面有很多关于 AI 的话题，有一些精美的 Cheat Sheet（知识速查表）


### Python参考书籍

#### 入门读物

1. 《Python编程从入门到实践》（*Python Crash Course*）- 著名的蟒蛇书，推荐
2. 《Python基础教程》（*Beginning Python From Novice to Professional*）- 入门经典
3. 《Python学习手册》（*Learning Python*）- 著名的老鼠书
4. 《Python编程导论》（*Introduction to computation and programming using Python*）- 我自己很喜欢这本书

#### 进阶读物

1. 《Python Cookbook》- 照着菜谱做，肯定不出错，进阶首选
2. 《流畅的Python》（*Fluent Python*）- 成为高手的必经之路
3. 《Effective Python：编写高质量Python代码的59个有效方法》（*Effective Python 59 Specific Ways to Write Better Python*）
4. 《Python高级编程》（*Expert Python Programming*）- 著名的花书
5. 《Python项目开发实战》- 有很多工程化项目开发的知识

#### 数据采集

1. 《Python 3网络爬虫开发实战》- 个人推荐
2. 《Python网络书籍擦剂》- 著名的穿山甲书

#### 数据分析

1. 《利用Python进行数据分析》（*Python for Data Analysis*）- pandas 作者写的书
2. 《Python数据科学手册》（*Python Data Science Handbook*）- O'Reilly 动物数系列的鬣蜥书
3. 《Python数据分析实战》（*Python Data Analytics With Pandas, NumPy, and Matplotlib*）- 个人推荐
4. 《Python数据可视化编程实战》（*Python Data Visualization Cookbook*）- 重点在数据可视化
5. 《深入浅出数据科学》（*Principles of Data Science*）- 我自己很喜欢这本书
6. 《面向数据科学家的实用统计学》（*Practical Statistics for Data Scientists*）- 很实用的统计学
7. 《数据科学入门》（*Data Science from Scratch*）

#### 机器学习

1. 《机器学习实战》（*Machine Learning in Aciton*）- 入门推荐
2. 《机器学习（鸢尾花数学大系：从加减乘除到机器学习）》- 姜伟生博士非常用心的作品
3. 《深度学习入门：基于Python的理论与实现》- 日本获奖书籍，入门推荐
4. 《深度学习》（*Deep Learning*）- 深度学习经典神作
5. 《动手学深度学习》（*Dive into Deep Learning*）- 阿斯顿·张和李沐强强联合出品
6. 《Python深度学习》（*Deep Learning with Python*）- 我自己很喜欢这本书
7. 《机器学习实战》（*Hands-On Machine Learning with Scikit-Learn, Keras, and Tensorflow*）- 著名的蜥蜴书，从机器学习到深度学习

#### 大模型

1. 《BERT基础教程：Transformer大模型实战》（*Getting Started with Google BERT*）- 五星好评书籍

2. 《Build a Large Language Model》
3. 《Hands-On Large Language Models》
4. 《大模型应用开发极简入门：基于GPT-4和ChatGPT》（*Developing Apps with GPT-4 and ChatGPT*）

#### 测试运维

1. 《Selenium自动化测试：基于Python语言》（*Learning Selenium Testing Tools with Python*）
2. 《pytest测试实战》（*Python Testing with pytest*）
3. 《Python自动化运维实战》（*Hands-On Enterprise Automation with Python*）
4. 《Python自动化运维技术与最佳实践》