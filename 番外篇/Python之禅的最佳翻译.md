## Zen of Python（Python之禅）

1. Beautiful is better than ugly. （优美比丑陋好）<br>
2. Explicit is better than implicit.（清晰比晦涩好）<br>
3. Simple is better than complex.（简单比复杂好）<br>
4. Complex is better than complicated.（复杂比错综复杂好）<br>
5. Flat is better than nested.（扁平比嵌套好）<br>
6. Sparse is better than dense.（稀疏比密集好）<br>
7. Readability counts.（可读性很重要）<br>
8. Special cases aren't special enough to break the rules.（特殊情况也不应该违反这些规则）<br>
9. Although practicality beats purity.（但现实往往并不那么完美）<br>
10. Errors should never pass silently.（异常不应该被静默处理）<br>
11. Unless explicitly silenced.（除非你希望如此）<br>
12. In the face of ambiguity, refuse the temptation to guess.（遇到模棱两可的地方，不要胡乱猜测）<br>
13. There should be one-- and preferably only one --obvious way to do it.（肯定有一种通常也是唯一一种最佳的解决方案）<br>
14. Although that way may not be obvious at first unless you're Dutch.（虽然这种方案并不是显而易见的，因为你不是那个荷兰人[^1]）<br>
15. Now is better than never.（现在开始做比不做好）<br>
16. Although never is often better than \*right\* now.（不做比盲目去做好[^2]）<br>
17. If the implementation is hard to explain, it's a bad idea.（如果一个实现方案难于理解，它通常不是一个好的方案）<br>
18. If the implementation is easy to explain, it may be a good idea.（如果一个实现方案易于理解，它很有可能是一个好的方案）<br>
19. Namespaces are one honking great idea -- let's do more of those!（命名空间非常有用，我们应当多加利用）<br>

[^1]:这里指的是 Python 之父 Guido van Rossumm。
[^2]:极限编程中的YAGNI原则

