## Python参考书籍

### 入门读物

1. 《Python基础教程》（*Beginning Python From Novice to Professional*）
2. 《Python学习手册》（*Learning Python*）
3. 《Python编程》（*Programming Python*）
4. 《Python Cookbook》
5. 《Python程序设计》（*Python Programming: An Introduction to Computer Science*）
6. 《Modern Python Cookbook》

### 进阶读物

1. 《Python核心编程》（*Core Python Applications Programming*）
2. 《流畅的Python》（*Fluent Python*）
3. 《Effective Python：编写高质量Python代码的59个有效方法》（*Effective Python 59 Specific Ways to Write Better Python*）
4. 《Python设计模式》（*Learning Python Design Patterns*）
5. 《Python高级编程》（*Expert Python Programming*）
6. 《Python性能分析与优化》（*Mastering Python High Performance*）

### Web框架

1. 《Django基础教程》（*Tango with Django*）
2. 《轻量级Django》（*Lightweight Django*）
3. 《Python Web开发：测试驱动方法》（*Test-Driven Development with Python*）
4. 《Web Development with Django Cookbook》
5. 《Test-Driven Development with Django》
6. 《Django Project Blueprints 》
7. 《Flask Web开发：基于Python的Web应用开发实战》（*Flask Web Development: Developing Web Applications with Python*）
8. 《深入理解Flask》（*Mastering Flask*）

### 数据采集

1. 《用Python写网络爬虫》（*Web Scraping with Python*）
2. 《精通Python爬虫框架Scrapy》（*Learning Scrapy*）
3. 《Python网络数据采集》（*Web Scraping with Python*）
4. 《Python爬虫开发与项目实战》
5. 《Python 3网络爬虫开发实战》

### 数据分析

1. 《利用Python进行数据分析》（*Python for Data Analysis*）
2. 《Python数据科学手册》（*Python Data Science Handbook*）
3. 《Python金融大数据分析》（*Python for Finance*）
4. 《Python数据可视化编程实战》（*Python Data Visualization Cookbook*）
5. 《Python数据处理》（*Data Wrangling with Python*）

### 机器学习

1. 《Python机器学习基础教程》（*Introduction to Machine Learning with Python*）
2. 《Python机器学习实践指南》（*Python Machine Learning Blueprints*）
3. 《Python Machine Learning Case Studies》
4. 《Python机器学习实践：测试驱动的开发方法》（*Thoughtful Machine Learning with Python A Test Driven Approach*）
5. 《Python机器学习经典实例》（*Python Machine Learning Cookbook*）
6. 《TensorFlow：实战Google深度学习框架》