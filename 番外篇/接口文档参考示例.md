## 接口文档参考示例

0. 用户登录 - **POST** `/api/login/`

      开发者：骆昊

      版本号：v1

      最后修改时间：

      接口说明：登录成功后，会生成或更新用户令牌（token）。

      使用帮助：测试数据库中预设了四个可供使用的账号，如下表所示。

      | 用户名     | 用户口令 | 角色         |
      | ---------- | -------- | ------------ |
      | jackfrued  | 123456   | 管理员       |
      | wangdachui | 123123   | 普通用户     |
      | hellokitty | 123123   | 房地产经理人 |
      | wuzetian   | 123456   | 房东         |

      请求参数：

      | 参数名   | 类型   | 是否必填 | 参数位置 | 说明     |
      | -------- | ------ | -------- | -------- | -------- |
      | username | 字符串 | 是       | 消息体   | 用户名   |
      | password | 字符串 | 是       | 消息体   | 用户口令 |

      响应信息：

      - 登录成功：

        ```JSON
        {
            "code": 30000,
            "message": "用户登录成功",
            "token": "f83e0f624e2311e9af1f00163e02b646"
        }
        ```

      - 登录失败：

        ```JSON
        {
            "code": 30001,
            "message": "用户名或密码错误"
        }
        ```

1. 发送短信验证码 - **GET** `/api/mobile_code/{国内手机号}/`

   开发者：骆昊

   版本号：v1

   接口说明：给指定手机号发送短信验证码的接口，手机号必须是国内手机号，作为路径参数写到URL中。接口显示短信发送成功时，指定的手机号并不会收到短息，因为使用的三方短信平台赠送的测试短信已经用完了。

   使用帮助：国内手机号暂不支持国际区号。

   请求参数：暂无。

   响应信息：

   - 请求成功：

     ```JSON
     {
         "code": 10001,
         "msg": "短信验证码发送成功"
     }
     ```

   - 两次请求间隔小于60秒：

     ```JSON
     {
         "code": 10002,
         "msg": "请不要在60秒以内重复发送手机验证码"
     }
     ```

   - 手机号无效：

     ```JSON
     {
         "code": 10003,
         "msg": "请提供有效的手机号"
     }
     ```

   - 短信服务平台故障：

     ```JSON
     {
         "code": 10004,
         "msg": "短信服务暂时无法使用"
     }
     ```

2. 获取所有省级行政单位 - **GET** `/api/districts/`

   开发者：骆昊

   版本号：v1

   接口说明：暂无。

   使用帮助：暂无。

   请求参数：暂无。

   响应信息：

   ```JSON
   [
       {
           "distid": 110000,
           "name": "北京市"
       },
       {
           "distid": 120000,
           "name": "天津市"
       }
   ]
   ```

3. 获取指定行政单位详情及其管辖的行政单位 - **GET** `/api/districts/{行政单位编号}/`

   开发者：骆昊

   版本号：v1

   接口说明：通过URL参数指定行政单位编号，如果行政单位编号为省级行政单位编号，则返回该省以及该省所辖市级行政单位的信息；如果行政单位编号为市级行政单位编号，则返回该市以及该市所辖区县的信息；如果行政单位编号为区县一级行政单位编号，则返回该区县的信息，下级行政单位`cities`属性值为`[]`。

   使用帮助：数据库中除四川省外其他行政单位的“intro”数据都没有录入，该字段可能为空字符串。

   请求参数：暂无。

   响应信息：

   ```JSON
   {
       "distid": 510000,
       "name": "四川省",
       "intro": "位于中国西南地区内陆，东连重庆，南邻云南、贵州，西接西藏，北界陕西、甘肃、青海，四川省总面积48.6万平方千米，省会成都。截至2018年底，四川省下辖18个省辖市，3个自治州，17个县级市，108个县，4个自治县，54个市辖区。",
       "cities": [
           {
               "distid": 510100,
               "name": "成都市"
           },
           {
               "distid": 510300,
               "name": "自贡市"
           },
           {
               "distid": 510400,
               "name": "攀枝花市"
           }
       ]
   }
   ```

4. 获取热门城市 - **GET** `/api/hotcities/`

   开发者：骆昊

   版本号：v1

   接口说明：暂无。

   使用帮助：暂无。

   请求参数：暂无。

   响应信息：

   ```JSON
   [
       {
           "distid": 110100,
           "name": "北京市"
       },
       {
           "distid": 120100,
           "name": "天津市"
       },
       {
           "distid": 130100,
           "name": "石家庄"
       }
   ]
   ```

5. 分页获取房地产经理人信息 - **GET** `/api/agents/`

   开发者：骆昊

   版本号：v1

   接口说明：经理人姓名按照前缀模糊匹配的方式进行处理；经理人服务星级是指经理人服务星级不得低于该星级；经理人是否持证只有0（未持证上岗）和1（持证上岗）两个选项。三个参数代表的筛选条件之间是而且的关系。返回结果为分页之后的房地产经理人信息。

   使用帮助：暂无。

   请求参数：

   | 参数名 | 类型   | 是否必填 | 参数位置 | 说明                                |
   | ------ | ------ | -------- | -------- | ---------------------------------|
   | name   | 字符串 | 否       | 查询参数 | 经理人姓名                          |
   | key    | 字符串 | 否       | 查询参数 | 经理人服务星级                      |
   | cert   | 字符串 | 否       | 查询参数 | 经理人是否持证                      |
   | page   | 整数   | 否       | 查询参数 | 页码，默认值为1                     |
   | size   | 整数   | 否       | 查询参数 | 页面大小，默认值为5，最大值不超过50 |

   响应信息：

   ```JSON
   {
       "count": 1,
       "next": null,
       "previous": null,
       "results": [
           {
               "agentid": 6,
               "estates": [
                   {
                       "estateid": 11,
                       "name": "灵芝新村",
                       "hot": 20
                   }
               ],
               "name": "肖利丽",
               "tel": "13040813886",
               "servstar": 4,
               "realstar": 4,
               "profstar": 4,
               "certificated": false
           }
       ]
   }
   ```

6. 新增房地产经理人 - **POST** `/api/agents/`

   开发者：骆昊

   版本号：v1

   接口说明：暂无。

   使用帮助：需要登录且拥有管理员权限，用户身份令牌在请求头中提供。

   请求参数：

   | 参数名       | 类型   | 是否必填 | 参数位置 | 说明             |
   | ------------ | ------ | -------- | -------- | ---------------- |
   | name         | 字符串 | 是       | 消息体   | 经理人姓名       |
   | tel          | 字符串 | 是       | 消息体   | 经理人手机       |
   | servstar     | 整数   | 否       | 消息体   | 默认值0          |
   | realstar     | 整数   | 否       | 消息体   | 默认值0          |
   | profstar     | 整数   | 否       | 消息体   | 默认值0          |
   | certificated | 整数   | 否       | 消息体   | 默认值0          |
   | token        | 字符串 | 是       | 请求头   | 用户身份认证令牌 |

   响应信息：

   - 新增成功 - 状态码**201**：

     ```JSON
     {
         "agentid": 8,
         "estates": [],
         "name": "孙小美",
         "tel": "13800991234",
         "servstar": 0,
         "realstar": 0,
         "profstar": 0,
         "certificated": false
     }
     ```

   - 未提供身份认证信息 - 状态码**401**：

     ```JSON
     {
         "detail": "不正确的身份认证信息。"
     }
     ```

   - 当前用户没有操作权限 - 状态码**403**：

     ```JSON
     {
         "detail": "您没有执行该操作的权限。"
     }
     ```

7. 编辑房地产经理人信息 - **PUT** `/api/agents/{房地产经理人编号}/`

    开发者：骆昊

    版本号：v1

    接口说明：暂无。

    使用帮助：需要登录且拥有管理员权限，用户身份令牌在请求头中提供。

    请求参数：

    | 参数名       | 类型   | 是否必填 | 参数位置 | 说明             |
    | ------------ | ------ | -------- | -------- | ---------------- |
    | name         | 字符串 | 是       | 消息体   | 经理人姓名       |
    | tel          | 字符串 | 是       | 消息体   | 经理人手机       |
    | servstar     | 整数   | 否       | 消息体   | 默认值0          |
    | realstar     | 整数   | 否       | 消息体   | 默认值0          |
    | profstar     | 整数   | 否       | 消息体   | 默认值0          |
    | certificated | 整数   | 否       | 消息体   | 默认值0          |
    | token        | 字符串 | 是       | 请求头   | 用户身份认证令牌 |

    响应信息：

    - 更新成功 - 状态码**200**：
      
     ```JSON
     {
         "agentid": 1,
         "estates": [
             {
                 "estateid": 1,
                 "name": "今日家园",
                 "hot": 20
             },
             {
                 "estateid": 2,
                 "name": "翡翠园",
                 "hot": 30
             },
             {
                 "estateid": 3,
                 "name": "万科城市花园",
                 "hot": 22
             }
         ],
         "name": "袁晓梦",
         "tel": "158173555285",
         "servstar": 5,
         "realstar": 4,
         "profstar": 3,
         "certificated": true
     }
     ```

    - 未提供身份认证信息 - 状态码**403** - 与新增类同
    - 当前用户没有操作权限 - 状态码**403** - 与新增类同

8. 删除房地产经理人 - **DELETE** `/api/agents/{房地产经理人编号}/`

    开发者：骆昊

    版本号：v1

    接口说明：暂无。

    使用说明：暂无。

    请求参数：

    | 参数名 | 类型   | 是否必填 | 参数位置 | 说明             |
    | ------ | ------ | -------- | -------- | ---------------- |
    | token  | 字符串 | 是       | 请求头   | 用户身份认证令牌 |

    响应信息：

    - 删除成功 - 状态码**204**
    - 未提供身份认证信息 - 状态码**403** - 与新增类同
    - 当前用户没有操作权限 - 状态码**403** - 与新增类同

9. 分页获取楼盘信息 - **GET** `/api/estates/`

    开发者：骆昊

    版本号：v1

    接口说明：经理人姓名按照前缀模糊匹配的方式进行处理；经理人服务星级是指经理人服务星级不得低于该星级；经理人是否持证只有0（未持证上岗）和1（持证上岗）两个选项。三个参数代表的筛选条件之间是而且的关系。返回结果为分页之后的房地产经理人信息。

    使用帮助：暂无。

    请求参数：

    | 参数名 | 类型   | 是否必填 | 参数位置 | 说明                                |
    | ------ | ------ | -------- | -------- | ----------------------------------- |
    | name   | 字符串 | 否       | 查询参数 | 楼盘名（模糊匹配）                  |
    | dist   | 字符串 | 否       | 查询参数 | 楼盘所在地区编号                    |
    | page   | 整数   | 否       | 查询参数 | 页码，默认值为1                     |
    | size   | 整数   | 否       | 查询参数 | 页面大小，默认值为5，最大值不超过50 |

    响应信息：

    ```JSON
    {
        "count": 16,
        "next": "https://120.77.222.217/api/estates/?page=2",
        "previous": null,
        "results": [
            {
                "estateid": 6,
                "district": {
                    "distid": 440303,
                    "name": "罗湖区"
                },
                "agents": [
                    {
                        "agentid": 2,
                        "name": "杨伟",
                        "tel": "13352939550",
                        "servstar": 3
                    },
                    {
                        "agentid": 4,
                        "name": "郭志鹏",
                        "tel": "13686810707",
                        "servstar": 4
                    }
                ],
                "name": "幸福里",
                "hot": 300,
                "intro": ""
            }
        ]
    }
    ```

10. 新增楼盘 - **POST** `/api/estates/`

  开发者：骆昊

  版本号：v1

  接口说明：暂无。

  使用帮助：需要登录且拥有管理员权限，用户身份令牌在请求头中提供。

  请求参数：

  | 参数名 | 类型   | 是否必填 | 参数位置 | 说明                     |
  | ------ | ------ | -------- | -------- | ------------------------ |
  | name   | 字符串 | 是       | 消息体   | 楼盘名称                 |
  | hot    | 整数   | 否       | 消息体   | 楼盘热度，默认值0        |
  | intro  | 字符串 | 否       | 消息体   | 楼盘介绍，默认值空字符串 |
  | distid | 整数   | 是       | 消息体   | 楼盘所在地区编号         |
  | token  | 字符串 | 是       | 请求头   | 用户身份认证令牌         |

  响应信息：

  - 新增成功 - 状态码**201**：
     ```JSON
     {
         "estateid": 17,
         "district": 510107,
         "name": "世纪锦苑",
         "hot": 100,
         "intro": ""
     }
     ```

  - 未提供身份认证信息 - 状态码**403**：
     ```JSON
     {
         "detail": "请提供有效的身份认证信息"
     }
     ```

  - 当前用户没有操作权限 - 状态码**403**：
     ```JSON
     {
         "detail": "You do not have permission to perform this action."
     }
     ```

11. 编辑楼盘信息 - **PUT** `/api/estates/{楼盘编号}`

12. 删除楼盘信 - **DELETE** `/api/estates/{楼盘编号}`

13. 获取所有户型信息 - **GET** `/api/housetypes/`

14. 新增户型 - **POST** `/api/housetypes/`

15. 编辑户型信息 - **PUT** `/api/housetypes/{户型编号}`

16. 删除户型 - **DELETE** `/api/housetypes/{户型编号}`

17. 分页获取房源信息 - **GET** `/api/houseinfos/`

     开发者：骆昊

     版本号：v1

     接口说明：暂无。

     使用帮助：暂无。

     请求参数：    

     | 参数名    | 类型   | 是否必填 | 参数位置 | 说明                                |
     | --------- | ------ | -------- | -------- | ----------------------------------- |
     | title     | 字符串 | 否       | 查询参数 | 房源标题关键词                      |
     | dist      | 整数   | 否       | 查询参数 | 楼盘所在地区编号                    |
     | min_price | 整数   | 否       | 查询参数 | 价格区间下限                        |
     | max_price | 整数   | 否       | 查询参数 | 价格区间上限                        |
     | type      | 整数   | 否       | 查询参数 | 户型编号                            |
     | page      | 整数   | 否       | 查询参数 | 页码，默认值为1                     |
     | size      | 整数   | 否       | 查询参数 | 页面大小，默认值为5，最大值不超过50 |

     响应信息：
     ```JSON
     {
         "count": 7,
         "next": "http://localhost:8000/api/houseinfos/?dist=440303&page=2",
         "previous": null,
         "results": [
     
         ]
     }
     ```

18. 查看房源详情 - **GET** `/api/houseinfos/{房源编号}`

19. 新增房源 - **POST** `/api/houseinfos/`

20. 编辑房源信息 - **PUT** `/api/houseinfos/{房源编号}`

21. 删除房源 - **DELETE** `/api/houseinfos/{房源编号}`

22. 随机获取指定数量的房源标签 - **GET** `/api/tags/`

23. 分页查看房源标签 - **GET** `/api/tags/`

24. 新增房源标签 - **POST** `/api/tags/`

25. 删除房源标签 - **DELETE**  `/api/tags/{房源编号}`

26. 查看房源的图片 - **GET** `/api/houseinfos/{房源编号}/photos/`

27. 为房源添加图片 - **POST** `/api/houseinfos/{房源编号}/photos/`

28. 删除房源图片 - **DELETE** `/api/houseinfos/{房源编号}/photos/{图片编号}`