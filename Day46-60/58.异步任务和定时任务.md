## 异步任务和定时任务

在Web应用中，如果一个请求执行了耗时间的操作或者该请求的执行时间无法确定，而且对于用户来说只需要知道服务器接收了他的请求，并不需要马上得到请求的执行结果，这样的操作我们就应该对其进行异步化处理。如果说**使用缓存是优化网站性能的第一要义**，那么将耗时间或执行时间不确定的任务**异步化则是网站性能优化的第二要义**，简单的说就是**能推迟做的事情都不要马上做**。

上一章节中讲到的发短信和上传文件到云存储为例，这两个操作前者属于时间不确定的操作（因为作为调用者，我们不能确定三方平台响应的时间），后者属于耗时间的操作（如果文件较大或者三方平台不稳定，都可能导致上传的时间较长），很显然，这两个操作都可以做异步化处理。

在 Python 项目中，我们可以使用三方库Celery来完成异步任务和定时任务，关于Celery的内容，请移步到[《使用Django开发商业项目》](../Day91-100/95.使用Django开发商业项目.md)一文。

